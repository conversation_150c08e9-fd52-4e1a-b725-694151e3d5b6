import AuthProvider from './shared/config/AuthProvider';

import DashboardHeader from './shared/layouts/DashboardHeader';
import WarehouseDropdown from './shared/layouts/WarehouseDropdown';

import '@/app/shared/styles/global.styles.css';

import 'primereact/resources/themes/viva-light/theme.css';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';

export const metadata = {
  title: 'Pack Lite',
  description: 'Pack Lite - Receipt Register',
  icons: {
    icon: [
      { url: '/parcel-box-package-icon.svg', sizes: '32x32' },
      { url: '/parcel-box-package-icon.svg', type: 'image/svg+xml' },
    ],
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className="min-h-screen flex flex-col">
        <AuthProvider>
          <DashboardHeader />
          <WarehouseDropdown />
          <main className="flex-grow m-2">{children}</main>
        </AuthProvider>
      </body>
    </html>
  );
}
