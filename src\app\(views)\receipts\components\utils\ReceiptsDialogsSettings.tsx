import { openConfirmDialog } from '@/app/shared/components/ReusableConfirmDialog';
import { Button } from 'primereact/button';

interface DeleteConfirmOptions {
  onAccept: () => void;
}

export function openDeleteConfirmDialog({ onAccept }: DeleteConfirmOptions) {
  openConfirmDialog({
    message: 'Are you sure you want to delete the selected items?',
    header: 'Delete Confirmation',
    acceptLabel: 'Yes, Delete',
    rejectLabel: 'Cancel',
    acceptClassName: 'p-button-danger',
    onAccept,
  });
}

export function getDeletionSuccessToast(handleUndo: () => void) {
  return {
    severity: 'success',
    summary: 'Deleted',
    detail: 'Items deleted.',
    life: 6000,
    content: (props: any) => (
      <div
        className="flex justify-between items-center gap-2"
        style={{ flex: 1 }}
      >
        <div className="text-900 font-medium">{props.message.detail}</div>
        <div>
          <Button
            label="Undo"
            icon="pi pi-undo"
            className="p-button-text p-button-sm"
            onClick={handleUndo}
          />
        </div>
      </div>
    ),
  };
}

export function getErrorToast(detail: string) {
  return {
    severity: 'error',
    summary: 'Error',
    detail,
  };
}

export function getRestoredToast() {
  return {
    severity: 'success',
    summary: 'Restored',
    detail: 'Items restored successfully',
  };
}

export function getCreatedToast() {
  return {
    severity: 'success',
    summary: 'Success',
    detail: 'Item created successfully',
  };
}

export function getUpdateSuccessToast() {
  return {
    severity: 'success',
    summary: 'Success',
    detail: 'Item updated successfully',
  };
}

export function getUpdateErrorToast(detail: string) {
  return {
    severity: 'error',
    summary: 'Error',
    detail,
  };
}
