'use client';

import { Dialog } from 'primereact/dialog';
import { ReactNode } from 'react';

type ReusableDialogProps = {
  visible: boolean;
  onHide: () => void;
  header?: ReactNode;
  footer?: ReactNode;
  children: ReactNode;
  modal?: boolean;
  position?:
    | 'center'
    | 'top'
    | 'bottom'
    | 'left'
    | 'right'
    | 'top-left'
    | 'top-right'
    | 'bottom-left'
    | 'bottom-right';
  maximizable?: boolean;
  resizable?: boolean;
  draggable?: boolean;
  dismissableMask?: boolean;
  closable?: boolean;
  showHeader?: boolean;
  breakpoints?: { [key: string]: string };
  style?: React.CSSProperties;
  className?: string;
};

export default function ReusableDialog({
  visible,
  onHide,
  header = 'Dialog',
  footer,
  children,
  modal = true,
  position = 'center',
  maximizable = false,
  resizable = true,
  draggable = true,
  dismissableMask = false,
  closable = true,
  showHeader = true,
  breakpoints = { '960px': '75vw', '640px': '100vw' },
  style = { width: '50vw' },
  className,
}: ReusableDialogProps) {
  return (
    <Dialog
      visible={visible}
      onHide={onHide}
      header={showHeader ? header : undefined}
      footer={footer}
      modal={modal}
      position={position}
      maximizable={maximizable}
      resizable={resizable}
      draggable={draggable}
      dismissableMask={dismissableMask}
      closable={closable}
      breakpoints={breakpoints}
      style={style}
      className={className}
    >
      {children}
    </Dialog>
  );
}
