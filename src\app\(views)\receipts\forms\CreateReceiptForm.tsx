'use client';

import { useEffect, useState } from 'react';
import { Button } from 'primereact/button';
import { Dropdown } from 'primereact/dropdown';
import { Calendar } from 'primereact/calendar';
import { InputText } from 'primereact/inputtext';
import { InputNumber } from 'primereact/inputnumber';
import { Checkbox } from 'primereact/checkbox';
import { InputTextarea } from 'primereact/inputtextarea';
import { MultiSelect } from 'primereact/multiselect';
import {
  createItem,
  fetchAssets,
  fetchVendors,
  fetchVessels,
  fetchNextReceiptNumber,
} from '../receipts.services';
import { PackageType } from '@/app/shared/models/global.enums';
import {
  IAsset,
  IItem,
  IVendor,
  IVessel,
} from '@/app/shared/models/global.interfaces';
import { IItemCreate } from '@/app/api/items/items.enums';
import { useAuthStore } from '@/store';

interface AddReceiptFormProps {
  onSuccess: () => void;
}

export default function AddReceiptForm({ onSuccess }: AddReceiptFormProps) {
  const { user } = useAuthStore();
  const [formData, setFormData] = useState<IItemCreate>({
    AssetId: '',
    VendorId: '',
    VesselId: '',
    ReceiptNumber: '',
    ReceiptDateTime: new Date(),
    SailingDate: null,
    NumberOfPackages: 0,
    NumberOfLineItems: 0,
    packageType: [],
    SAPDelivery: '',
    DescriptionOfGoods: undefined,
    IsQuarantined: false,
    LoadListItem: false,
    CCU: '',
    Manifest: '',
    IsDeleted: false,
    ReceivedByUserId: user?.UserId ?? '',
    WarehouseId: '',
    PONumber: '',
    SupplierDeliveryNoteNumber: '',
  });
  const [loading, setLoading] = useState(false);
  const [assets, setAssets] = useState<IAsset[]>([] as IAsset[]);
  const [vendors, setVendors] = useState<IVendor[]>([] as IVendor[]);
  const [vessels, setVessels] = useState<IVessel[]>([] as IVessel[]);
  const [dataLoaded, setDataLoaded] = useState({
    assets: false,
    vendors: false,
    vessels: false,
  });
  // Add a state to track touched fields
  const [touchedFields, setTouchedFields] = useState({
    SupplierDeliveryNoteNumber: false,
    DescriptionOfGoods: false,
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        const [assetsData, vendorsData, vesselsData] = await Promise.all([
          fetchAssets(),
          fetchVendors(),
          fetchVessels(),
        ]);
        setAssets(assetsData);
        setVendors(vendorsData);
        setVessels(vesselsData);
        setDataLoaded({ assets: true, vendors: true, vessels: true });
      } catch (error) {
        console.error('Failed to fetch assets or vendors:', error);
      }
    };
    loadData();
  }, []);

  // New effect to auto-generate Receipt Number.
  useEffect(() => {
    async function loadReceiptNumber() {
      try {
        const candidateReceiptNumber = await fetchNextReceiptNumber();
        setFormData((prev) => ({
          ...prev,
          ReceiptNumber: candidateReceiptNumber,
        }));
      } catch (error) {
        console.error('Error loading receipt number:', error);
      }
    }
    loadReceiptNumber();
  }, []);

  const handleChange = (key: keyof IItem, value: any) => {
    setFormData((prev) => ({ ...prev, [key]: value }));
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      await createItem(formData);
      onSuccess();
    } catch (error) {
      console.error('Failed to create item:', error);
    } finally {
      setLoading(false);
    }
  };

  // Check if required fields are filled
  const isFormValid = () => {
    return (
      !!formData.ReceiptNumber &&
      !!formData.ReceiptDateTime &&
      !!formData.VendorId &&
      !!formData.SupplierDeliveryNoteNumber &&
      formData.NumberOfPackages > 0 &&
      !!formData.packageType &&
      !!formData.NumberOfLineItems &&
      !!formData.DescriptionOfGoods
    );
  };

  return (
    <div className="p-4">
      <h2 className="text-lg text-gray-400">Mandatory Information</h2>
      <div className="mb-3">
        <label>Receipt Number</label>
        <InputText
          value={formData.ReceiptNumber}
          onChange={(e) => handleChange('ReceiptNumber', e.target.value)}
          className="w-full"
          disabled={true}
          placeholder="Generating..."
        />
      </div>
      <div className="mb-3">
        <label>Receipt Date & Time</label>
        <Calendar
          dateFormat="dd/mm/yy"
          value={formData.ReceiptDateTime}
          onChange={(e) => handleChange('ReceiptDateTime', e.value)}
          showTime
          className="w-full"
        />
      </div>
      <div className="mb-3">
        <label>Vendor</label>
        <Dropdown
          value={formData.VendorId}
          options={vendors}
          onChange={(e) => handleChange('VendorId', e.value)}
          optionLabel="Name"
          optionValue="VendorId"
          placeholder="Select a Vendor"
          filter
          className="w-full"
          disabled={!dataLoaded.vendors}
          loading={!dataLoaded.vendors}
        />
      </div>
      <div className="mb-3">
        <label>Supplier Delivery Note Number</label>
        <InputText
          value={formData.SupplierDeliveryNoteNumber}
          onChange={(e) =>
            handleChange('SupplierDeliveryNoteNumber', e.target.value)
          }
          onBlur={(e) => {
            if (!touchedFields.SupplierDeliveryNoteNumber) {
              setTouchedFields((prev) => ({
                ...prev,
                SupplierDeliveryNoteNumber: true,
              }));
            }

            if (!e.target.value) {
              handleChange('SupplierDeliveryNoteNumber', '');
            }
          }}
          className="w-full"
          placeholder="Delivery Number"
          invalid={
            touchedFields.SupplierDeliveryNoteNumber &&
            !formData.SupplierDeliveryNoteNumber
          }
        />
      </div>
      <div className="mb-3">
        <label>Number of Packages</label>
        <InputNumber
          value={formData.NumberOfPackages}
          onValueChange={(e) => handleChange('NumberOfPackages', e.value)}
          min={1}
          className="w-full"
        />
      </div>
      <div className="mb-3">
        <label>Package Type</label>
        <MultiSelect
          value={formData.packageType}
          options={Object.values(PackageType).map((type) => ({
            label: type,
            value: type,
          }))}
          onChange={(e) => handleChange('packageType', e.value)}
          placeholder="Select Package Types"
          filter
          className="w-full"
          display="comma"
        />
      </div>
      <div className="mb-3">
        <label>Number of Line Items</label>
        <InputNumber
          value={formData.NumberOfLineItems}
          onValueChange={(e) => handleChange('NumberOfLineItems', e.value)}
          min={1}
          className="w-full"
        />
      </div>
      <div className="mb-3">
        <label>Description of Goods</label>
        <InputTextarea
          value={formData.DescriptionOfGoods || ''}
          onChange={(e) => handleChange('DescriptionOfGoods', e.target.value)}
          onBlur={(e) => {
            if (!touchedFields.DescriptionOfGoods) {
              setTouchedFields((prev) => ({
                ...prev,
                DescriptionOfGoods: true,
              }));
            }

            if (!e.target.value) {
              handleChange('DescriptionOfGoods', '');
            }
          }}
          rows={3}
          className="w-full"
          placeholder="Enter Description of Goods"
          invalid={
            touchedFields.DescriptionOfGoods && !formData.DescriptionOfGoods
          }
        />
      </div>

      <div className="mb-5">
        <label>Received By</label>
        <InputText value={user?.Name} disabled className="w-full" />
      </div>

      {/* Separator */}
      <hr className="mt-4 mb-1 text-gray-300" />

      {/* Optional Fields */}
      <h2 className="text-lg text-gray-400">(Optional)</h2>
      <div className="mb-3">
        <label>Asset</label>
        <Dropdown
          value={formData.AssetId}
          options={assets}
          onChange={(e) => handleChange('AssetId', e.value)}
          optionLabel="Name"
          optionValue="AssetId"
          placeholder="Select an Asset"
          filter
          className="w-full"
          disabled={!dataLoaded.assets}
          loading={!dataLoaded.assets}
        />
      </div>
      <div className="mb-3">
        <label>P/O Number</label>
        <InputText
          value={formData.PONumber}
          onChange={(e) => handleChange('PONumber', e.target.value)}
          className="w-full"
          placeholder="Enter P/O Number"
        />
      </div>

      <div className="mb-3">
        <label>Sailing Date</label>
        <Calendar
          dateFormat="dd/mm/yy"
          value={formData.SailingDate}
          onChange={(e) => handleChange('SailingDate', e.value)}
          showTime
          className="w-full"
          placeholder="Enter Sailing Date"
          showButtonBar
        />
      </div>
      <div className="mb-3">
        <label>SAP Delivery</label>
        <InputText
          value={formData.SAPDelivery}
          onChange={(e) => handleChange('SAPDelivery', e.target.value)}
          className="w-full"
          placeholder="Enter SAP Delivery"
        />
      </div>
      <div className="mb-3">
        <label>CCU</label>
        <InputText
          value={formData.CCU}
          onChange={(e) => handleChange('CCU', e.target.value)}
          className="w-full"
          placeholder="Enter CCU"
        />
      </div>
      <div className="mb-3">
        <label>Manifest</label>
        <InputText
          value={formData.Manifest}
          onChange={(e) => handleChange('Manifest', e.target.value)}
          className="w-full"
          placeholder="Enter Manifest"
        />
      </div>
      <div className="mb-3 flex align-items-center">
        <Checkbox
          inputId="isQuarantined"
          checked={formData.IsQuarantined}
          onChange={(e) => handleChange('IsQuarantined', e.checked)}
          className="mr-2"
        />
        <label htmlFor="isQuarantined" className="cursor-pointer">
          Is Quarantined
        </label>
      </div>
      <div className="mb-3 flex align-items-center">
        <Checkbox
          inputId="loadListItem"
          checked={formData.LoadListItem}
          onChange={(e) => handleChange('LoadListItem', e.checked)}
          className="mr-2"
        />
        <label htmlFor="loadListItem" className="cursor-pointer">
          Load List Item
        </label>
      </div>

      <div className="mb-3">
        <label>Vessel</label>
        <Dropdown
          value={formData.VesselId}
          options={vessels}
          onChange={(e) => handleChange('VesselId', e.value)}
          optionLabel="Name"
          optionValue="VesselId"
          placeholder="Select a Vessel"
          filter
          className="w-full"
          disabled={!dataLoaded.vessels}
          loading={!dataLoaded.vessels}
        />
      </div>

      <Button
        label="Submit"
        icon="pi pi-check"
        onClick={handleSubmit}
        loading={loading}
        disabled={loading || !isFormValid()}
        className="w-full mt-3"
      />
    </div>
  );
}
