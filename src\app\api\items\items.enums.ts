export interface IItemCreate {
  AssetId: string;
  VesselId: string;
  VendorId: string;
  ReceiptNumber: string;
  ReceiptDateTime: Date;
  SailingDate?: Date | null;
  NumberOfPackages: number;
  NumberOfLineItems: number;
  packageType: string[];
  SAPDelivery?: string;
  DescriptionOfGoods?: string;
  PONumber?: string | null;
  SupplierDeliveryNoteNumber?: string | null;
  IsQuarantined: boolean;
  LoadListItem: boolean;
  CCU?: string;
  Manifest?: string;
  IsDeleted: boolean;
  ReceivedByUserId: string;
  WarehouseId?: string | null;
}
