import { useRef, useState } from 'react';
import { Toolbar } from 'primereact/toolbar';
import { Button } from 'primereact/button';
import ReceiptsFilters from './ReceiptsFilters';
import ReusableDialog from '@/app/shared/components/ReusableDialog';
import { IItem } from '@/app/shared/models/global.interfaces';
import AddReceiptForm from '../forms/CreateReceiptForm';
import { KeyedMutator } from 'swr';

import ReusableToast from '@/app/shared/components/ReusableToast';
import { deleteItems, restoreItems } from '../receipts.services';
import ReusableConfirmDialog from '@/app/shared/components/ReusableConfirmDialog';

import {
  openDeleteConfirmDialog,
  getDeletionSuccessToast,
  getErrorToast,
  getRestoredToast,
  getCreatedToast,
} from './utils/ReceiptsDialogsSettings';

import { useWarehouseStore } from '@/store';

interface ReceiptsToolbarProps {
  selectedReceipts: IItem[];
  globalFilter: string;
  setGlobalFilter: (value: string) => void;
  filterQuarantined: boolean;
  setFilterQuarantined: (value: boolean) => void;
  refetchItems: KeyedMutator<IItem[]>;
  userRole?: string;
}

function ReceiptsToolbar({
  selectedReceipts,
  globalFilter,
  setGlobalFilter,
  filterQuarantined,
  setFilterQuarantined,
  refetchItems,
  userRole,
}: ReceiptsToolbarProps) {
  const [dialogVisible, setDialogVisible] = useState(false);
  const toast = useRef<any>(null);

  const selectedWarehouse = useWarehouseStore(
    (state) => state.selectedWarehouse
  );

  const handleSuccessfulAdd = async () => {
    setDialogVisible(false);
    await refetchItems();

    toast.current?.show(getCreatedToast());
  };

  const handleDelete = async () => {
    try {
      await deleteItems(selectedReceipts);
      await refetchItems();
      toast.current?.show(getDeletionSuccessToast(handleUndo));
    } catch (error) {
      toast.current?.show(getErrorToast('Failed to delete items'));
    }
  };

  const handleUndo = async () => {
    try {
      await restoreItems(selectedReceipts);
      await refetchItems();
      toast.current?.show(getRestoredToast());
    } catch (error) {
      toast.current?.show(getErrorToast('Failed to restore items'));
    }
  };

  return (
    <>
      <Toolbar
        className="mb-3"
        start={
          <div className="flex items-center gap-2">
            {userRole !== 'read' && (
              <div className="relative group">
                <Button
                  label="Add Receipt"
                  icon="pi pi-plus"
                  onClick={() => setDialogVisible(true)}
                  disabled={selectedWarehouse?.WarehouseId === 'all'}
                  className="peer"
                />
                {selectedWarehouse?.WarehouseId === 'all' && (
                  <div className="absolute bottom-full left-0 mb-1 w-max bg-black text-white text-xs px-2 py-1 rounded shadow opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity">
                    Please select a warehouse to add a receipt
                  </div>
                )}
              </div>
            )}
            {userRole !== 'read' && (
              <Button
                label="Delete"
                icon="pi pi-trash"
                severity="danger"
                disabled={!selectedReceipts.length}
                onClick={() =>
                  openDeleteConfirmDialog({
                    onAccept: handleDelete,
                  })
                }
                className="mr-2"
              />
            )}

            {/* <Button
              label="Export"
              icon="pi pi-download"
              disabled={!selectedReceipts.length}
            /> */}
            <p className="text-2xl font-semibold ml-4">
              Receipts{' '}
              {selectedReceipts.length
                ? `(${selectedReceipts.length})`
                : 'Register'}
            </p>
          </div>
        }
        end={
          <ReceiptsFilters
            globalFilter={globalFilter}
            setGlobalFilter={setGlobalFilter}
            filterQuarantined={filterQuarantined}
            setFilterQuarantined={setFilterQuarantined}
          />
        }
      />
      <ReusableDialog
        visible={dialogVisible}
        onHide={() => setDialogVisible(false)}
        header="Add Receipt"
      >
        <AddReceiptForm onSuccess={handleSuccessfulAdd} />
      </ReusableDialog>

      <ReusableToast ref={toast} />

      <ReusableConfirmDialog />
    </>
  );
}

export default ReceiptsToolbar;
