// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init
generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "sqlserver"
  url      = env("DATABASE_URL")
}

model Asset {
  AssetId String @id @default(dbgenerated("(newid())")) @db.UniqueIdentifier
  Name    String @unique
  Items   Item[]
  Warehouses AssetWarehouse[]
  IsDeleted Boolean @default(false)
}

model Vendor {
  VendorId String @id @default(dbgenerated("(newid())")) @db.UniqueIdentifier
  Name     String @unique
  Items    Item[]
  Warehouses VendorWarehouse[]
  IsDeleted Boolean @default(false)
}

model Warehouse {
  WarehouseId String          @id @default(dbgenerated("(newid())")) @db.UniqueIdentifier
  Name        String
  Items       Item[]
  Users       UserWarehouse[]
  Assets      AssetWarehouse[]
  Vessels     VesselWarehouse[]
  Vendors     VendorWarehouse[]
  IsDeleted   Boolean         @default(false)
}

model User {
  UserId     String          @id @default(dbgenerated("(newid())")) @db.UniqueIdentifier
  Name       String
  Email      String          @unique
  Items      Item[]
  Warehouses UserWarehouse[]
  Role       String // enum
  IsDeleted  Boolean @default(false)
}

model UserWarehouse {
  UserWarehouseId String @id @default(dbgenerated("(newid())")) @db.UniqueIdentifier
  UserId          String @db.UniqueIdentifier
  WarehouseId     String @db.UniqueIdentifier

  User      User      @relation(fields: [UserId], references: [UserId])
  Warehouse Warehouse @relation(fields: [WarehouseId], references: [WarehouseId])
}

model Vessel {
  VesselId String @id @default(dbgenerated("(newid())")) @db.UniqueIdentifier
  Name     String @unique
  Items    Item[]
  Warehouses VesselWarehouse[]
  IsDeleted Boolean @default(false)
}

model AssetWarehouse {
  AssetWarehouseId String @id @default(dbgenerated("(newid())")) @db.UniqueIdentifier
  AssetId         String @db.UniqueIdentifier
  WarehouseId     String @db.UniqueIdentifier

  Asset     Asset     @relation(fields: [AssetId], references: [AssetId])
  Warehouse Warehouse @relation(fields: [WarehouseId], references: [WarehouseId])
}

model VesselWarehouse {
  VesselWarehouseId String @id @default(dbgenerated("(newid())")) @db.UniqueIdentifier
  VesselId        String @db.UniqueIdentifier
  WarehouseId     String @db.UniqueIdentifier

  Vessel    Vessel    @relation(fields: [VesselId], references: [VesselId])
  Warehouse Warehouse @relation(fields: [WarehouseId], references: [WarehouseId])
}

model VendorWarehouse {
  VendorWarehouseId String @id @default(dbgenerated("(newid())")) @db.UniqueIdentifier
  VendorId        String @db.UniqueIdentifier
  WarehouseId     String @db.UniqueIdentifier

  Vendor    Vendor    @relation(fields: [VendorId], references: [VendorId])
  Warehouse Warehouse @relation(fields: [WarehouseId], references: [WarehouseId])
}

model Item {
  ItemId                     String   @id @default(dbgenerated("(newid())")) @db.UniqueIdentifier
  WarehouseId                String?  @db.UniqueIdentifier
  ReceivedByUserId           String   @db.UniqueIdentifier
  AssetId                    String?   @db.UniqueIdentifier
  VendorId                   String   @db.UniqueIdentifier
  VesselId                   String?  @db.UniqueIdentifier
  ReceiptNumber              String   @unique
  ReceiptDateTime            DateTime
  SailingDate                DateTime?
  PONumber                   String?
  SupplierDeliveryNoteNumber String?
  NumberOfPackages           Int
  NumberOfLineItems          Int?
  packageType                String // enum
  SAPDelivery                String?
  DescriptionOfGoods         String?
  IsQuarantined              Boolean  @default(false)
  LoadListItem               Boolean  @default(false)
  CCU                        String?
  Manifest                   String?

  IsDeleted Boolean @default(false)

  // Quarantine fields
  DescriptionOfNonConformance String?
  NCRNumber                   String?
  Status                      String? // Enum (Pending, Resolved, etc.)
  DateClosed                  DateTime?
  ElapsedTimeDays             Int?


  Warehouse       Warehouse?       @relation(fields: [WarehouseId], references: [WarehouseId], onDelete: NoAction, onUpdate: NoAction)
  ReceivedUser    User             @relation(fields: [ReceivedByUserId], references: [UserId], onDelete: NoAction, onUpdate: NoAction)
  Asset           Asset?            @relation(fields: [AssetId], references: [AssetId], onDelete: NoAction, onUpdate: NoAction)
  Vendor          Vendor           @relation(fields: [VendorId], references: [VendorId], onDelete: NoAction, onUpdate: NoAction)
  Vessel          Vessel?          @relation(fields: [VesselId], references: [VesselId], onDelete: NoAction, onUpdate: NoAction)
}
