'use client';

import useS<PERSON> from 'swr';
import { useRef, useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import ReusableToast from '@/app/shared/components/ReusableToast';
import { But<PERSON> } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { textEditor } from '../../receipts/utils/editors.utils';
import { openDeleteConfirmDialog } from '../../receipts/components/utils/ReceiptsDialogsSettings';
import ReusableConfirmDialog from '@/app/shared/components/ReusableConfirmDialog';
import { fetchAssets, createAsset, updateAsset } from '../admin.services';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash } from '@fortawesome/free-solid-svg-icons';

export default function AssetsManagement() {
  const { data: assets, error, mutate } = useSWR('/admin/assets', fetchAssets);
  const [newAsset, setNewAsset] = useState({ Name: '' });
  const [editingRowId, setEditingRowId] = useState<string | null>(null);
  const toastRef = useRef<any>(null);

  const onRowEditInit = (e: any) => setEditingRowId(e.data.AssetId);
  const onRowEditCancel = () => setEditingRowId(null);

  const onRowEditComplete = async (e: any) => {
    try {
      await updateAsset(e.newData.AssetId, { Name: e.newData.Name });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Updated',
        detail: 'Asset updated',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update asset',
      });
    } finally {
      setEditingRowId(null);
    }
  };

  const handleCreate = async () => {
    try {
      await createAsset(newAsset);
      setNewAsset({ Name: '' });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Created',
        detail: 'Asset created',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to create asset',
      });
    }
  };

  const handleDelete = async (a: any) => {
    try {
      await updateAsset(a.AssetId, { IsDeleted: true });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Deleted',
        detail: 'Asset deleted',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to delete asset',
      });
    }
  };

  const confirmDelete = (rowData: any) => {
    openDeleteConfirmDialog({ onAccept: () => handleDelete(rowData) });
  };

  if (!assets) return <p>Loading...</p>;
  if (error) return <p>Error loading assets</p>;

  return (
    <>
      <ReusableToast ref={toastRef} />
      <h1>Assets Management</h1>
      <div className="flex gap-2 my-2">
        <InputText
          placeholder="Name"
          value={newAsset.Name}
          onChange={(e) => setNewAsset({ Name: e.target.value })}
        />
        <Button
          disabled={!newAsset.Name}
          label="Create Asset"
          onClick={handleCreate}
        />
      </div>

      <DataTable
        value={assets}
        editMode="row"
        dataKey="AssetId"
        size="small"
        stripedRows
        onRowEditInit={onRowEditInit}
        onRowEditCancel={onRowEditCancel}
        onRowEditComplete={onRowEditComplete}
      >
        {/* <Column field="AssetId" header="ID" /> */}
        <Column
          field="Name"
          header="Name"
          editor={(opts) => textEditor(opts)}
        />
        <Column rowEditor headerStyle={{ width: '4rem' }} />
        <Column
          body={(rowData) => (
            <Button
              icon={<FontAwesomeIcon icon={faTrash} />}
              className="p-button-text p-button-rounded p-button-danger"
              onClick={() => confirmDelete(rowData)}
              tooltip="Delete"
              tooltipOptions={{ position: 'top' }}
            />
          )}
          headerStyle={{ width: '6rem' }}
        />
      </DataTable>

      <ReusableConfirmDialog />
    </>
  );
}
