import { forwardRef, useImperativeHandle, useRef, ComponentProps } from 'react';
import { Toast } from 'primereact/toast';

// Extend the props from Toast component if applicable
export interface ReusableToastProps extends ComponentProps<typeof Toast> {}
export interface ReusableToastHandles {
  show: (options: {
    severity: 'info' | 'warn' | 'error' | 'success';
    summary: string;
    detail?: string;
    life?: number;
  }) => void;
  clear(): void;
}

const ReusableToast = forwardRef<ReusableToastHandles, ReusableToastProps>(
  function ReusableToast(props, ref) {
    const toastRef = useRef<Toast>(null);

    useImperativeHandle(ref, () => ({
      show: (options) => {
        toastRef.current?.show({ life: 5000, ...options });
      },
      clear: () => toastRef.current?.clear(),
    }));

    return <Toast position="top-center" ref={toastRef} {...props} />;
  }
);

export default ReusableToast;
