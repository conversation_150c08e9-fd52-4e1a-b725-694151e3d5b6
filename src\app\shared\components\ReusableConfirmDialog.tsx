'use client';

import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';

export default function ReusableConfirmDialog() {
  return <ConfirmDialog />;
}

/**
 * Function to trigger the confirmation dialog externally
 */
export function openConfirmDialog(props: {
  message: string;
  header?: string;
  icon?: string;
  acceptLabel?: string;
  rejectLabel?: string;
  acceptIcon?: string;
  rejectIcon?: string;
  acceptClassName?: string;
  rejectClassName?: string;
  footer?: React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
  onAccept: () => void;
  onReject?: () => void;
}) {
  confirmDialog({
    message: props.message,
    header: props.header ?? 'Confirmation',
    icon: props.icon ?? 'pi pi-exclamation-triangle',
    acceptLabel: props.acceptLabel ?? 'Yes',
    rejectLabel: props.rejectLabel ?? 'No',
    acceptIcon: props.acceptIcon,
    rejectIcon: props.rejectIcon,
    acceptClassName: props.acceptClassName,
    rejectClassName: props.rejectClassName,
    footer: props.footer,
    style: props.style,
    className: props.className,
    accept: props.onAccept,
    reject: props.onReject,
    defaultFocus: 'reject',
  });
}
