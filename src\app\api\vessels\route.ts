import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  const parts = request.url.split('/');
  const maybeId = parts[parts.length - 1];

  // If the path ends in "vessels", list them; otherwise get by ID
  if (maybeId === 'vessels') {
    try {
      const vessels = await prisma.vessel.findMany({
        where: { IsDeleted: false },
        orderBy: { Name: 'asc' },
      });
      return NextResponse.json(vessels);
    } catch (error) {
      return NextResponse.json(
        { error: 'Error listing vessels' },
        { status: 500 }
      );
    }
  } else {
    try {
      const vessel = await prisma.vessel.findUnique({
        where: { VesselId: maybeId, IsDeleted: false },
      });
      if (!vessel) {
        return NextResponse.json({ error: 'Not found' }, { status: 404 });
      }
      return NextResponse.json(vessel);
    } catch (error) {
      return NextResponse.json(
        { error: 'Error fetching vessel' },
        { status: 500 }
      );
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    const { Name } = await request.json();
    const newVessel = await prisma.vessel.create({ data: { Name } });
    return NextResponse.json(newVessel, { status: 201 });
  } catch (error) {
    return NextResponse.json(
      { error: 'Error creating vessel' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const parts = request.url.split('/');
    const vesselId = parts[parts.length - 1];
    const { Name, IsDeleted } = await request.json();
    const updated = await prisma.vessel.update({
      where: { VesselId: vesselId },
      data: {
        ...(Name && { Name }),
        ...(IsDeleted !== undefined && { IsDeleted }),
      },
    });
    return NextResponse.json(updated);
  } catch (error) {
    return NextResponse.json(
      { error: 'Error updating vessel' },
      { status: 500 }
    );
  }
}
