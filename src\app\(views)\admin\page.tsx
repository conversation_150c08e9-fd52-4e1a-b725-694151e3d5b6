'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/auth.store';
import { TabView, TabPanel } from 'primereact/tabview';
import Image from 'next/image';

import UsersManagement from './users/page';
import WarehousesManagement from './warehouses/page';
import VesselsManagement from './vessels/page';
import AssetsManagement from './assets/page';
import VendorsManagement from './vendors/page';
import StatisticsManagement from './statistics/page';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUsers,
  faHome,
  faShip,
  faDollarSign,
  faChartBar,
  IconDefinition,
} from '@fortawesome/free-solid-svg-icons';

type TabType = {
  key: string;
  label: string;
  component: React.ReactNode;
  isCustomIcon: boolean;
  icon: IconDefinition | string;
};

const tabs: TabType[] = [
  {
    key: 'users',
    label: 'Users',
    icon: faUsers,
    component: <UsersManagement />,
    isCustomIcon: false,
  },
  {
    key: 'warehouses',
    label: 'Warehouses',
    icon: faHome,
    component: <WarehousesManagement />,
    isCustomIcon: false,
  },
  {
    key: 'vessels',
    label: 'Vessels',
    icon: faShip,
    component: <VesselsManagement />,
    isCustomIcon: false,
  },
  {
    key: 'assets',
    label: 'Assets',
    icon: '/Pictogrammers-Material-Pier-crane.svg',
    component: <AssetsManagement />,
    isCustomIcon: true,
  },
  {
    key: 'vendors',
    label: 'Vendors',
    icon: faDollarSign,
    component: <VendorsManagement />,
    isCustomIcon: false,
  },
  {
    key: 'statistics',
    label: 'Statistics',
    icon: faChartBar,
    component: <StatisticsManagement />,
    isCustomIcon: false,
  },
];

const AdminView = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const { user } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    if (user && user.Role !== 'admin' && user.Role !== 'system_admin') {
      router.replace('/receipts'); // Redirect non-admin users
    }
  }, [user, router]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const hash = window.location.hash.replace('#', '');
      const index = tabs.findIndex((tab) => tab.key === hash);
      if (index !== -1) setActiveIndex(index);
    }
  }, []);

  const handleTabChange = (e: any) => {
    setActiveIndex(e.index);
    window.location.hash = tabs[e.index].key;
  };

  if (!user) {
    return <p>Loading...</p>;
  }

  if (user.Role !== 'admin' && user.Role !== 'system_admin') {
    return null;
  }

  return (
    <div className="p-4">
      <TabView activeIndex={activeIndex} onTabChange={handleTabChange}>
        {tabs.map((tab, index) => (
          <TabPanel
            key={tab.key}
            header={
              <div className="flex items-center gap-2">
                {tab.isCustomIcon ? (
                  <div
                    style={{
                      width: '18px',
                      height: '18px',
                      transform: 'translateY(-1px)',
                    }}
                  >
                    <Image
                      src={tab.icon as string}
                      alt={tab.label}
                      width={18}
                      height={18}
                      className="text-base font-light"
                      style={{
                        filter:
                          index === activeIndex
                            ? 'invert(37%) sepia(74%) saturate(1217%) hue-rotate(212deg) brightness(91%) contrast(98%)'
                            : 'invert(55%) sepia(11%) saturate(11%) hue-rotate(314deg) brightness(92%) contrast(87%)',
                        transition: 'filter 0.2s ease',
                      }}
                    />
                  </div>
                ) : (
                  <FontAwesomeIcon
                    icon={tab.icon as IconDefinition}
                    style={{
                      fontSize: '.9rem',
                      transform: 'translateY(-1px)',
                      color: index === activeIndex ? '#5472D4' : '#898989',
                      transition: 'color 0.2s ease',
                    }}
                    className="text-base font-light"
                  />
                )}
                <span className="text-sm">{tab.label}</span>
              </div>
            }
          >
            {tab.component}
          </TabPanel>
        ))}
      </TabView>
    </div>
  );
};

export default AdminView;
