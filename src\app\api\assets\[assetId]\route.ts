import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET one asset
export async function GET(
  _request: NextRequest,
  context: { params: Promise<{ assetId: string }> }
) {
  try {
    const { assetId } = await context.params;
    const asset = await prisma.asset.findUnique({
      where: { AssetId: assetId, IsDeleted: false },
    });
    if (!asset) {
      return NextResponse.json({ error: 'Not found' }, { status: 404 });
    }
    return NextResponse.json(asset);
  } catch {
    return NextResponse.json(
      { error: 'Error fetching asset' },
      { status: 500 }
    );
  }
}

// PUT: update asset or soft-delete
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ assetId: string }> }
) {
  try {
    const { assetId } = await context.params;
    const data = await request.json();
    await prisma.asset.update({
      where: { AssetId: assetId },
      data,
    });
    return NextResponse.json({ message: 'Asset updated' });
  } catch {
    return NextResponse.json(
      { error: 'Error updating asset' },
      { status: 500 }
    );
  }
}
