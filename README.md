# 📦 Pack Lite

<div align="center">
  <img src="./public/comet-logo.png" alt="Comet Analysis Logo" style="margin: 1rem 0;" />

[![Next.js](https://img.shields.io/badge/Next.js-15.2.1-black)](https://nextjs.org/)
[![Node](https://img.shields.io/badge/Node-22.x-green)](https://nodejs.org/)
[![Prisma](https://img.shields.io/badge/Prisma-6.4.1-blue)](https://www.prisma.io/)
[![Azure AD](https://img.shields.io/badge/Azure%20AD-Auth-0078D4?logo=microsoft-azure)](https://azure.microsoft.com/)

</div>

## 🚀 Project Overview

Pack Lite (Peterson Receipt Register) is a web-based application designed to manage and track receipt records for <PERSON>'s logistics operations. This system allows users to register, monitor, and manage receipts for various assets, vendors, vessels, and warehouses. The application includes features for quarantine management, user role-based access control, and comprehensive reporting capabilities.

## 🔑 Key Features

- **Receipt Management**: Create, view, and manage receipt records with detailed information
- **Quarantine Register**: Track and manage quarantined items with non-conformance details
- **Admin Dashboard**: Manage assets, vendors, vessels, and user access (admin, read, write)
- **Role-Based Access Control**: Different permission levels for various user roles
- **Search and Filtering**: Advanced search capabilities across all receipt data
- **Responsive UI**: Modern interface built with React, Next.js, and PrimeReact components
- **Multi-Tenant Architecture**: Support for multiple warehouses with isolated data access

## 🏭 Multi-Tenant Architecture

The Peterson Receipt Register implements a multi-tenant architecture that allows:

- **Warehouse Isolation**: Each warehouse operates as a separate tenant with its own data
- **User-Warehouse Association**: Users can be associated with one or more warehouses
- **Role-Based Permissions**: Different access levels based on user roles across warehouses
- **Centralised Administration**: Global admin users can manage all warehouses
- **Data Segregation**: Ensures data privacy and security between different operational units

This architecture enables Peterson to manage multiple warehouses or operational units through a single application instance while maintaining proper data isolation and access control.

## 🛠 Technologies Used

- **Next.js**: React framework for server-side rendering and API routes
- **React**: Frontend library for building user interfaces
- **Prisma**: Next-generation ORM for database access
- **PrimeReact**: UI component library for React applications
- **TypeScript**: Typed JavaScript for better developer experience
- **Tailwind CSS**: Utility-first CSS framework
- **SQL Server**: Enterprise database for robust data storage
- **NextAuth.js**: Authentication solution for Next.js applications
- **SWR**: React Hooks for data fetching
- **Zustand**: Lightweight global state management solution

## 🧰 Installation Instructions

### 📋 Prerequisites

- Node.js 22.x
- SQL Server database
- Git

### ⚙️ Setup

1. Clone the repository:

   ```bash
   git clone https://github.com/cometanalysis/peterson-receipt-register.git
   cd peterson-receipt-register
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   ```
4. Update the `.env` file with your database connection details and other configuration:

5. Run database migrations:

   ```bash
   npx prisma migrate dev
   ```

6. Generate Prisma client:
   ```bash
   npx prisma generate
   ```

## 💻 Usage

### 🏗 Development Mode

To run the application in development mode:

```bash
npm run dev
```

The application will be available at `http://localhost:3000`.

### 🚢 Production Build

To create a production build:

```bash
npm run build
```

To start the production server:

```bash
npm start
```

### 🐳 Docker Deployment

The application includes a Dockerfile for containerised deployment:

```bash
docker build -t peterson-receipt-register .
docker run -p 3000:3000 peterson-receipt-register
```

## 🗂 Application Structure

- **`/src/app/(views)`**: Contains the main application views (receipts, quarantine, admin)
- **`/src/app/api`**: API routes for data operations
- **`/src/app/shared`**: Shared components, models, and utilities
- **`/prisma`**: Database schema and migrations
- **`/public`**: Static assets

## 🗃 Database Schema

The application uses Prisma ORM with a SQL Server database. The main entities include:

- **Items (Receipts)**: Core receipt records with detailed information
- **Assets**: Any type of asset
- **Vendors**: Suppliers providing the goods
- **Vessels**: Ships associated with receipts
- **Warehouses**: Storage locations for received items
- **Users**: System users with role-based permissions

## 🤝 Contributing

Please ensure your code follows the project's coding standards by running:

```bash
npm run lint
```
