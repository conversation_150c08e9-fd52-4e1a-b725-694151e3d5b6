{"name": "peterson-receipt-register", "version": "1.0.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write ."}, "engines": {"node": "22.x"}, "repository": {"type": "git", "url": "git+https://github.com/cometanalysis/peterson-receipt-register.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/cometanalysis/peterson-receipt-register/issues"}, "homepage": "https://github.com/cometanalysis/peterson-receipt-register#readme", "description": "Receipt register application for <PERSON>", "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@prisma/client": "^6.4.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "axios": "^1.8.3", "chart.js": "^4.4.8", "chartjs-plugin-datalabels": "^2.2.0", "dotenv": "^16.4.7", "eslint": "^9.21.0", "eslint-config-next": "^15.2.1", "eslint-config-prettier": "^10.0.2", "next": "^15.2.1", "next-auth": "^4.24.11", "prettier": "^3.5.3", "primeicons": "^7.0.0", "primereact": "^10.9.2", "react": "^19.0.0", "react-dom": "^19.0.0", "swr": "^2.3.3", "zustand": "^5.0.3"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.9", "@types/mssql": "^9.1.7", "autoprefixer": "^10.4.20", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "prisma": "^6.4.1"}}