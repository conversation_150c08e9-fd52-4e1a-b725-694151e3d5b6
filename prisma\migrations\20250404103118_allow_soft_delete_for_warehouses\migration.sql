BEGIN TRY

BEGIN TRAN;

-- AlterTable
ALTER TABLE [dbo].[Asset] DROP CONSTRAINT [Asset_AssetId_df];
ALTER TABLE [dbo].[Asset] ADD CONSTRAINT [Asset_AssetId_df] DEFAULT (newid()) FOR [AssetId];

-- AlterTable
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_ItemId_df];
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_ItemId_df] DEFAULT (newid()) FOR [ItemId];

-- AlterTable
ALTER TABLE [dbo].[User] DROP CONSTRAINT [User_UserId_df];
ALTER TABLE [dbo].[User] ADD CONSTRAINT [User_UserId_df] DEFAULT (newid()) FOR [UserId];

-- AlterTable
ALTER TABLE [dbo].[UserWarehouse] DROP CONSTRAINT [UserWarehouse_UserWarehouseId_df];
ALTER TABLE [dbo].[UserWarehouse] ADD CONSTRAINT [UserWarehouse_UserWarehouseId_df] DEFAULT (newid()) FOR [UserWarehouseId];

-- AlterTable
ALTER TABLE [dbo].[Vendor] DROP CONSTRAINT [Vendor_VendorId_df];
ALTER TABLE [dbo].[Vendor] ADD CONSTRAINT [Vendor_VendorId_df] DEFAULT (newid()) FOR [VendorId];

-- AlterTable
ALTER TABLE [dbo].[Vessel] DROP CONSTRAINT [Vessel_VesselId_df];
ALTER TABLE [dbo].[Vessel] ADD CONSTRAINT [Vessel_VesselId_df] DEFAULT (newid()) FOR [VesselId];

-- AlterTable
ALTER TABLE [dbo].[Warehouse] DROP CONSTRAINT [Warehouse_WarehouseId_df];
ALTER TABLE [dbo].[Warehouse] ADD CONSTRAINT [Warehouse_WarehouseId_df] DEFAULT (newid()) FOR [WarehouseId];
ALTER TABLE [dbo].[Warehouse] ADD [IsDeleted] BIT NOT NULL CONSTRAINT [Warehouse_IsDeleted_df] DEFAULT 0;

COMMIT TRAN;

END TRY
BEGIN CATCH

IF @@TRANCOUNT > 0
BEGIN
    ROLLBACK TRAN;
END;
THROW

END CATCH
