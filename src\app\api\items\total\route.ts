import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// Gets the total number of items in the database, including soft-deleted items.
// Mainly used to generate receipts numbers and for statistics.
export async function GET() {
  try {
    const totalItems = await prisma.item.count();
    return NextResponse.json(totalItems);
  } catch (error) {
    console.error('Error fetching total items:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
