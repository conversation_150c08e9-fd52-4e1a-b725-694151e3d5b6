import { getSession } from 'next-auth/react';
import type { Session } from 'next-auth';

// Session cache to reduce redundant API calls
let sessionCache: {
  data: Session | null;
  timestamp: number;
  expiresIn: number;
} = {
  data: null,
  timestamp: 0,
  expiresIn: 60000, // 1 minute cache
};

/**
 * Gets a session from cache or fetches a new one if the cache is invalid
 * This reduces redundant API calls to the session endpoint
 */
export const getCachedSession = async (): Promise<Session | null> => {
  const now = Date.now();
  
  // Return cached session if it's still valid
  if (sessionCache.data && now - sessionCache.timestamp < sessionCache.expiresIn) {
    return sessionCache.data;
  }
  
  // Otherwise fetch a new session
  try {
    const session = await getSession();
    
    // Update cache
    sessionCache = {
      data: session,
      timestamp: now,
      expiresIn: 60000,
    };
    
    return session;
  } catch (error) {
    console.error('Error fetching session:', error);
    return null;
  }
};

/**
 * Invalidates the current session cache, forcing a fresh fetch on next request
 */
export const invalidateSessionCache = (): void => {
  sessionCache = {
    data: null,
    timestamp: 0,
    expiresIn: 60000,
  };
};
