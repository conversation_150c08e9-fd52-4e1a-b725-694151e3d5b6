import React from 'react';

interface ReusableTagProps {
  value: string;
  color?: 'info' | 'success' | 'warning' | 'danger' | 'custom' | undefined;
  rounded?: boolean;
  className?: string;
  customColor?: string;
}

/**
 * A reusable tag component that can be used throughout the application
 * @param value - The text to display in the tag
 * @param color - The color of the tag (info, success, warning, danger, custom)
 * @param rounded - Whether the tag should have rounded corners
 * @param className - Additional CSS classes to apply to the tag
 * @param customColor - Custom background color (only used when color is 'custom')
 */
function ReusableTag({
  value,
  color = 'info',
  rounded = true,
  className = '',
  customColor,
}: ReusableTagProps) {
  const baseClass = 'p-tag';
  const roundedClass = rounded ? 'p-tag-rounded' : '';
  const colorClass = color !== 'custom' ? `p-tag-${color}` : '';
  
  const style = color === 'custom' && customColor 
    ? { backgroundColor: customColor, color: '#ffffff' } 
    : undefined;
  
  return (
    <span 
      className={`${baseClass} ${roundedClass} ${colorClass} ${className}`}
      style={style}
    >
      {value}
    </span>
  );
}

export default ReusableTag;
