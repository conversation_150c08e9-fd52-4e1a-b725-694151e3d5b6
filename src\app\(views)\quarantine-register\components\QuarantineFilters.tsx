import { InputText } from 'primereact/inputtext';
import { But<PERSON> } from 'primereact/button';

interface QuarantineFiltersProps {
  globalFilter: string;
  setGlobalFilter: (value: string) => void;
}

function QuarantineFilters({
  globalFilter,
  setGlobalFilter,
}: QuarantineFiltersProps) {
  return (
    <div className="p-inputgroup flex-1">
      <InputText
        type="search"
        placeholder="Search..."
        value={globalFilter}
        onChange={(e) => setGlobalFilter(e.target.value)}
      />
      <Button icon="pi pi-search" />
    </div>
  );
}

export default QuarantineFilters;
