import { create } from 'zustand';
import { fetchUser } from '@/app/(views)/admin/admin.services';
import { useWarehouseStore } from '@/store';
import { signIn, signOut } from 'next-auth/react';
import { Session } from 'next-auth';

interface User {
  UserId: string;
  Name: string;
  Role: string;
  Email: string;
}

interface AuthStoreInterface {
  authenticated: boolean;
  setAuthentication: (val: boolean) => void;
  user: User | null;
  setUser: (user: Partial<User>) => void;
  fetchAndSetUser: (sessionData?: Session | null) => Promise<void>;
  retriggerFetchUser: () => Promise<void>;
  isUserLoading: boolean;
  login: () => Promise<void>;
  logout: () => Promise<void>;
}

// Create a non-persistent auth store (no localStorage)
export const useAuthStore = create<AuthStoreInterface>()((set, get) => ({
  authenticated: false,
  user: null,
  isUserLoading: false,

  setAuthentication: (val) => set({ authenticated: val }),

  setUser: (user) => {
    set((state) => ({
      user: state.user ? { ...state.user, ...user } : null,
    }));

    // Immediately refresh warehouses when user changes
    useWarehouseStore.getState().fetchAndSetWarehouses();
  },

  fetchAndSetUser: async (sessionData = null) => {
    set({ isUserLoading: true });
    try {
      // Use provided session data if available
      let session = sessionData;
      
      // Only fetch session if not provided
      if (!session) {
        const sessionResponse = await fetch('/api/auth/session');
        session = await sessionResponse.json();
      }

      if (session && session.user) {
        // If we have a session, use the user ID from the session
        const userId = session.user.id;
        if (userId) {
          const userData = await fetchUser(userId);
          set({ user: userData, authenticated: true });

          // Trigger warehouse update after fetching user
          useWarehouseStore.getState().fetchAndSetWarehouses();
          return;
        }
      } else {
        // No valid session, ensure we're logged out
        set({ authenticated: false, user: null });
      }
    } catch (error) {
      set({ authenticated: false });
      console.error('Error fetching user:', error);
    } finally {
      set({ isUserLoading: false });
    }
  },

  retriggerFetchUser: async () => {
    await get().fetchAndSetUser();
  },

  login: async () => {
    try {
      set({ isUserLoading: true });
      // Use NextAuth's signIn function
      await signIn('azure-ad', {
        callbackUrl: window.location.origin,
        redirect: true,
      });
    } catch (error) {
      console.error('Login error:', error);
    }
    set({ isUserLoading: false });
  },

  logout: async () => {
    try {
      set({ isUserLoading: true });

      // Clear auth state first
      set({ authenticated: false, user: null });

      // Then sign out from NextAuth
      await signOut({ callbackUrl: '/login' });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      set({ isUserLoading: false });
    }
  },
}));
