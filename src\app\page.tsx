'use client';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useWarehouseStore } from '@/store';
import { Card } from 'primereact/card';
import { ProgressSpinner } from 'primereact/progressspinner';

export default function Home() {
  const router = useRouter();
  const selectedWarehouse = useWarehouseStore(
    (state) => state.selectedWarehouse
  );

  useEffect(() => {
    if (selectedWarehouse !== null) {
      router.push('/receipts');
    }
  }, [selectedWarehouse, router]);

  if (selectedWarehouse === null) {
    return (
      <div className="p-4">
        <Card className="shadow-lg p-4">
          <div className="flex justify-center items-center py-10">
            <ProgressSpinner />
          </div>
        </Card>
      </div>
    );
  }

  return null; // or a loader if needed
}
