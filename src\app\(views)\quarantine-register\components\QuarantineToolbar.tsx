import { Toolbar } from 'primereact/toolbar';
import QuarantineFilters from './QuarantineFilters';

interface QuarantineToolbarProps {
  globalFilter: string;
  setGlobalFilter: (value: string) => void;
}

function QuarantineToolbar({
  globalFilter,
  setGlobalFilter,
}: QuarantineToolbarProps) {
  return (
    <Toolbar
      className="mb-3 flex justify-between"
      start={<p className="text-2xl font-semibold">Quarantine Register</p>}
      end={
        <QuarantineFilters
          globalFilter={globalFilter}
          setGlobalFilter={setGlobalFilter}
        />
      }
    />
  );
}

export default QuarantineToolbar;
