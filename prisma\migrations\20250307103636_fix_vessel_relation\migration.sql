BEGIN TRY

BEGIN TRAN;

-- DropForeignKey
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_AssetId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_ReceivedByUserId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_VendorId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_WarehouseId_fkey];

-- AlterTable
ALTER TABLE [dbo].[Asset] DROP CONSTRAINT [Asset_AssetId_df];
ALTER TABLE [dbo].[Asset] ADD CONSTRAINT [Asset_AssetId_df] DEFAULT (newid()) FOR [AssetId];

-- AlterTable
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_ItemId_df];
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_ItemId_df] DEFAULT (newid()) FOR [ItemId];
ALTER TABLE [dbo].[Item] ADD [IsDeleted] BIT NOT NULL CONSTRAINT [Item_IsDeleted_df] DEFAULT 0,
[VesselId] UNIQUEIDENTIFIER;

-- AlterTable
ALTER TABLE [dbo].[User] DROP CONSTRAINT [User_UserId_df];
ALTER TABLE [dbo].[User] ADD CONSTRAINT [User_UserId_df] DEFAULT (newid()) FOR [UserId];

-- AlterTable
ALTER TABLE [dbo].[UserWarehouse] DROP CONSTRAINT [UserWarehouse_UserWarehouseId_df];
ALTER TABLE [dbo].[UserWarehouse] ADD CONSTRAINT [UserWarehouse_UserWarehouseId_df] DEFAULT (newid()) FOR [UserWarehouseId];

-- AlterTable
ALTER TABLE [dbo].[Vendor] DROP CONSTRAINT [Vendor_VendorId_df];
ALTER TABLE [dbo].[Vendor] ADD CONSTRAINT [Vendor_VendorId_df] DEFAULT (newid()) FOR [VendorId];

-- AlterTable
ALTER TABLE [dbo].[Vessel] DROP CONSTRAINT [Vessel_VesselId_df];
ALTER TABLE [dbo].[Vessel] ADD CONSTRAINT [Vessel_VesselId_df] DEFAULT (newid()) FOR [VesselId];

-- AlterTable
ALTER TABLE [dbo].[Warehouse] DROP CONSTRAINT [Warehouse_WarehouseId_df];
ALTER TABLE [dbo].[Warehouse] ADD CONSTRAINT [Warehouse_WarehouseId_df] DEFAULT (newid()) FOR [WarehouseId];

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_WarehouseId_fkey] FOREIGN KEY ([WarehouseId]) REFERENCES [dbo].[Warehouse]([WarehouseId]) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_ReceivedByUserId_fkey] FOREIGN KEY ([ReceivedByUserId]) REFERENCES [dbo].[User]([UserId]) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_AssetId_fkey] FOREIGN KEY ([AssetId]) REFERENCES [dbo].[Asset]([AssetId]) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_VendorId_fkey] FOREIGN KEY ([VendorId]) REFERENCES [dbo].[Vendor]([VendorId]) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_VesselId_fkey] FOREIGN KEY ([VesselId]) REFERENCES [dbo].[Vessel]([VesselId]) ON DELETE NO ACTION ON UPDATE NO ACTION;

COMMIT TRAN;

END TRY
BEGIN CATCH

IF @@TRANCOUNT > 0
BEGIN
    ROLLBACK TRAN;
END;
THROW

END CATCH
