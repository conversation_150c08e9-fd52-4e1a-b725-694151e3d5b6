import { useState, useEffect, useRef } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { formatDateTime } from '@/app/shared/utils/dates.utils';
import {
  updateItem,
  fetchAssets,
  fetchVendors,
  fetchVessels,
} from '@/app/(views)/receipts/receipts.services';
import { PackageType } from '@/app/shared/models/global.enums';
import { IItem } from '@/app/shared/models/global.interfaces';
import { KeyedMutator } from 'swr';
import { InputSwitch } from 'primereact/inputswitch';
import {
  textEditor,
  dateTimeEditor,
  numberEditor,
  dropdownEditor,
  multiSelectEditor,
} from '../utils/editors.utils';
import ReusableToast from '@/app/shared/components/ReusableToast';
import {
  getUpdateSuccessToast,
  getUpdateErrorToast,
} from './utils/ReceiptsDialogsSettings';
import { useWarehouseStore } from '@/store';
import PackageTypeTags from '@/app/shared/components/PackageTypeTags';

interface ReceiptsTableProps {
  filteredReceipts: IItem[];
  selectedReceipts: IItem[];
  setSelectedReceipts: (value: IItem[]) => void;
  globalFilter: string;
  refetchItems: KeyedMutator<IItem[]>;
  userRole?: string;
}

function ReceiptsTable({
  filteredReceipts,
  selectedReceipts,
  setSelectedReceipts,
  globalFilter,
  refetchItems,
  userRole,
}: ReceiptsTableProps) {
  // Lookup data states
  const [assets, setAssets] = useState<any[]>([]);
  const [vendors, setVendors] = useState<any[]>([]);
  const [vessels, setVessels] = useState<any[]>([]);
  const [isEditing, setIsEditing] = useState(false);

  const toast = useRef<any>(null);
  const selectedWarehouse = useWarehouseStore(
    (state) => state.selectedWarehouse
  );

  useEffect(() => {
    if (!isEditing) return;
    // If data already exists, use the cache and do not refetch.
    if (assets.length && vendors.length && vessels.length) return;

    async function loadData() {
      try {
        const [assetsData, vendorsData, vesselsData] = await Promise.all([
          fetchAssets(),
          fetchVendors(),
          fetchVessels(),
        ]);
        setAssets(assetsData);
        setVendors(vendorsData);
        setVessels(vesselsData);
      } catch (error) {
        console.error('Error loading lookup data', error);
      }
      setIsEditing(false);
    }

    loadData();
  }, [isEditing, assets, vendors, vessels]);

  useEffect(() => {
    // Trigger refetch when selected warehouse changes
    refetchItems();
  }, [selectedWarehouse, refetchItems]);

  const onRowEditInit = () => {
    setIsEditing(true);
  };

  const onRowEditComplete = async (e: any) => {
    const { newData } = e;
    try {
      await updateItem(newData.ItemId, newData);
      toast.current?.show(getUpdateSuccessToast());
      await refetchItems();
    } catch (error) {
      toast.current?.show(getUpdateErrorToast('Failed to update item'));
    } finally {
      setIsEditing(false);
    }
  };

  return (
    <>
      <DataTable<IItem[]>
        value={filteredReceipts}
        paginator
        rows={20}
        rowsPerPageOptions={[10, 20, 50, 100]}
        stripedRows
        className="w-full"
        scrollable
        scrollHeight="60vh"
        selectionMode={userRole !== 'read' ? 'checkbox' : null}
        selection={userRole !== 'read' ? (selectedReceipts ?? []) : []}
        onSelectionChange={
          userRole !== 'read'
            ? (e: { value: IItem[] }) => setSelectedReceipts(e.value)
            : undefined
        }
        globalFilter={globalFilter}
        sortField="ReceiptDateTime"
        sortOrder={-1}
        size="small"
        rowHover
        globalFilterFields={[
          'ReceiptNumber',
          'PONumber',
          'SupplierDeliveryNoteNumber',
          'packageType',
          'SAPDelivery',
          'Manifest',
          'CCU',
          'Warehouse.Name',
          'Asset.Name',
          'Vendor.Name',
          'PONumber',
          'SupplierDeliveryNoteNumber',
          'ReceivedUser.Name',
          'Vessel.Name',
        ]}
        dataKey="ItemId"
        editMode="row"
        onRowEditInit={onRowEditInit}
        onRowEditComplete={onRowEditComplete}
      >
        {userRole !== 'read' && (
          <Column frozen selectionMode="multiple" style={{ width: '3rem' }} />
        )}

        <Column
          field="ReceiptNumber"
          header="Receipt Number"
          sortable
          editor={(options: {
            value: string;
            editorCallback?: (value: string) => void;
          }) => textEditor(options)}
          style={{ minWidth: '200px' }}
        />

        <Column
          field="ReceivedByUserId"
          header="Received By"
          body={(rowData: IItem) => rowData.ReceivedUser?.Name}
          style={{ minWidth: '180px' }}
        />

        <Column
          field="ReceiptDateTime"
          header="Receipt Date & Time"
          sortable
          editor={(options: {
            value: string;
            editorCallback?: (value: string) => void;
          }) => dateTimeEditor(options)}
          body={(rowData: IItem) => formatDateTime(rowData.ReceiptDateTime)}
          style={{ minWidth: '240px' }}
        />

        <Column
          field="AssetId"
          header="Asset"
          editor={(options: {
            value: string;
            editorCallback?: (value: string) => void;
          }) =>
            dropdownEditor(
              options,
              assets,
              'Name',
              'AssetId',
              'Select an Asset'
            )
          }
          body={(rowData: IItem) => rowData.Asset?.Name}
          style={{ minWidth: '180px' }}
        />

        <Column
          field="VendorId"
          header="Vendor"
          editor={(options: {
            value: string;
            editorCallback?: (value: string) => void;
          }) =>
            dropdownEditor(
              options,
              vendors,
              'Name',
              'VendorId',
              'Select a Vendor'
            )
          }
          body={(rowData: IItem) => rowData.Vendor?.Name}
          style={{ minWidth: '200px' }}
        />

        <Column
          field="PONumber"
          header="P/O Number"
          sortable
          editor={(options: {
            value: string;
            editorCallback?: (value: string) => void;
          }) => textEditor(options)}
          style={{ minWidth: '170px' }}
        />

        <Column
          field="SupplierDeliveryNoteNumber"
          header="Supplier Delivery Note Number"
          editor={(options: {
            value: string;
            editorCallback?: (value: string) => void;
          }) => textEditor(options)}
          style={{
            minWidth: '200px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        />

        <Column
          field="SAPDelivery"
          header="SAP Delivery"
          style={{ minWidth: '150px' }}
          editor={(options: {
            value: string;
            editorCallback?: (value: string) => void;
          }) => textEditor(options)}
        />

        <Column
          field="NumberOfPackages"
          header="# of Packages"
          editor={(options: {
            value: number;
            editorCallback?: (value: number) => void;
          }) => numberEditor(options)}
          style={{ minWidth: '120px', textAlign: 'center' }}
        />

        <Column
          field="packageType"
          header="Type of Package"
          body={(rowData) => (
            <PackageTypeTags packageType={rowData.packageType} />
          )}
          editor={(options: {
            value: string | string[];
            editorCallback?: (value: string[]) => void;
          }) =>
            multiSelectEditor(
              options,
              Object.values(PackageType).map((type) => ({
                label: type,
                value: type,
              })),
              'label',
              'value',
              'Select Package Types'
            )
          }
          style={{ minWidth: '200px' }}
        />

        <Column
          field="NumberOfLineItems"
          header="# of Line Items"
          style={{ minWidth: '120px', textAlign: 'center' }}
          editor={(options: {
            value: number;
            editorCallback?: (value: number) => void;
          }) => numberEditor(options)}
        />

        <Column
          field="DescriptionOfGoods"
          header="Description of Goods"
          style={{ minWidth: '200px' }}
          editor={(options: {
            value: string;
            editorCallback?: (value: string) => void;
          }) => textEditor(options)}
        />

        <Column
          field="IsQuarantined"
          header="Quarantined"
          editor={(options: {
            value: boolean;
            editorCallback?: (value: boolean) => void;
          }) => (
            <InputSwitch
              checked={options.value}
              onChange={(e) =>
                options.editorCallback && options.editorCallback(e.value)
              }
            />
          )}
          body={(rowData: IItem) => (
            <i
              className={`pi pi-${
                rowData.IsQuarantined ? 'lock' : 'unlock'
              } text-xl`}
              style={{
                fontSize: '1.3rem',
                color: rowData.IsQuarantined
                  ? 'var(--peterson-primary)'
                  : 'green'
              }}
            />
          )}
          style={{ minWidth: '120px', textAlign: 'center' }}
        />

        <Column
          field="LoadListItem"
          header="Load List"
          editor={(options: {
            value: boolean;
            editorCallback?: (value: boolean) => void;
          }) => (
            <InputSwitch
              checked={options.value}
              onChange={(e) =>
                options.editorCallback && options.editorCallback(e.value)
              }
            />
          )}
          body={(rowData: IItem) => (
            <i
              className={`pi pi-${
                rowData.LoadListItem ? 'check-circle' : 'times-circle'
              } text-xl`}
              style={{
                fontSize: '1.3rem',
                color: rowData.LoadListItem
                  ? 'green'
                  : 'var(--peterson-primary)',
              }}
            />
          )}
          style={{ minWidth: '120px', textAlign: 'center' }}
        />

        <Column
          field="CCU"
          header="CCU"
          style={{ minWidth: '120px' }}
          editor={(options: {
            value: string;
            editorCallback?: (value: string) => void;
          }) => textEditor(options)}
        />

        <Column
          field="Manifest"
          header="Manifest"
          style={{ minWidth: '120px' }}
          editor={(options: {
            value: string;
            editorCallback?: (value: string) => void;
          }) => textEditor(options)}
        />

        {selectedWarehouse?.WarehouseId === 'all' && (
          <Column
            header="Warehouse"
            body={(rowData: IItem) => rowData.Warehouse?.Name}
            style={{ minWidth: '180px' }}
          />
        )}

        <Column
          field="SailingDate"
          header="Sailing Date"
          body={(rowData: IItem) =>
            rowData.SailingDate ? formatDateTime(rowData.SailingDate) : ''
          }
          style={{ minWidth: '180px' }}
          editor={(options: {
            value: string;
            editorCallback?: (value: string) => void;
          }) => dateTimeEditor(options)}
        />

        <Column
          field="VesselId"
          header="Vessel"
          editor={(options: {
            value: string;
            editorCallback?: (value: string) => void;
          }) =>
            dropdownEditor(
              options,
              vessels,
              'Name',
              'VesselId',
              'Select a Vessel'
            )
          }
          body={(rowData: IItem) => rowData.Vessel?.Name}
          style={{ minWidth: '180px' }}
        />

        {userRole !== 'read' && (
          <Column
            frozen
            header="Actions"
            alignFrozen="right"
            rowEditor
            headerStyle={{ width: '10%', minWidth: '8rem' }}
            bodyStyle={{ textAlign: 'center' }}
          />
        )}
      </DataTable>

      <ReusableToast ref={toast} />
    </>
  );
}

export default ReceiptsTable;
