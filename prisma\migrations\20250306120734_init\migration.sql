/*
  Warnings:

  - The primary key for the `Asset` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Item` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `User` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `UserWarehouse` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Vendor` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Vessel` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `Warehouse` table will be changed. If it partially fails, the table could be left without primary key constraint.

*/
BEGIN TRY

BEGIN TRAN;

-- DropForeignKey
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_AssetId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_ReceivedByUserId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_VendorId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_WarehouseId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[UserWarehouse] DROP CONSTRAINT [UserWarehouse_UserId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[UserWarehouse] DROP CONSTRAINT [UserWarehouse_WarehouseId_fkey];

-- AlterTable
ALTER TABLE [dbo].[Asset] DROP CONSTRAINT [Asset_pkey];
ALTER TABLE [dbo].[Asset] ALTER COLUMN [AssetId] NVARCHAR(1000) NOT NULL;
ALTER TABLE [dbo].[Asset] ADD CONSTRAINT Asset_pkey PRIMARY KEY CLUSTERED ([AssetId]);

-- AlterTable
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_pkey];
ALTER TABLE [dbo].[Item] ALTER COLUMN [ItemId] NVARCHAR(1000) NOT NULL;
ALTER TABLE [dbo].[Item] ALTER COLUMN [WarehouseId] NVARCHAR(1000) NULL;
ALTER TABLE [dbo].[Item] ALTER COLUMN [ReceivedByUserId] NVARCHAR(1000) NOT NULL;
ALTER TABLE [dbo].[Item] ALTER COLUMN [AssetId] NVARCHAR(1000) NOT NULL;
ALTER TABLE [dbo].[Item] ALTER COLUMN [VendorId] NVARCHAR(1000) NOT NULL;
ALTER TABLE [dbo].[Item] ADD CONSTRAINT Item_pkey PRIMARY KEY CLUSTERED ([ItemId]);

-- AlterTable
ALTER TABLE [dbo].[User] DROP CONSTRAINT [User_pkey];
ALTER TABLE [dbo].[User] ALTER COLUMN [UserId] NVARCHAR(1000) NOT NULL;
ALTER TABLE [dbo].[User] ADD CONSTRAINT User_pkey PRIMARY KEY CLUSTERED ([UserId]);

-- AlterTable
ALTER TABLE [dbo].[UserWarehouse] DROP CONSTRAINT [UserWarehouse_pkey];
ALTER TABLE [dbo].[UserWarehouse] ALTER COLUMN [UserWarehouseId] NVARCHAR(1000) NOT NULL;
ALTER TABLE [dbo].[UserWarehouse] ALTER COLUMN [UserId] NVARCHAR(1000) NOT NULL;
ALTER TABLE [dbo].[UserWarehouse] ALTER COLUMN [WarehouseId] NVARCHAR(1000) NOT NULL;
ALTER TABLE [dbo].[UserWarehouse] ADD CONSTRAINT UserWarehouse_pkey PRIMARY KEY CLUSTERED ([UserWarehouseId]);

-- AlterTable
ALTER TABLE [dbo].[Vendor] DROP CONSTRAINT [Vendor_pkey];
ALTER TABLE [dbo].[Vendor] ALTER COLUMN [VendorId] NVARCHAR(1000) NOT NULL;
ALTER TABLE [dbo].[Vendor] ADD CONSTRAINT Vendor_pkey PRIMARY KEY CLUSTERED ([VendorId]);

-- AlterTable
ALTER TABLE [dbo].[Vessel] DROP CONSTRAINT [Vessel_pkey];
ALTER TABLE [dbo].[Vessel] ALTER COLUMN [VesselId] NVARCHAR(1000) NOT NULL;
ALTER TABLE [dbo].[Vessel] ADD CONSTRAINT Vessel_pkey PRIMARY KEY CLUSTERED ([VesselId]);

-- AlterTable
ALTER TABLE [dbo].[Warehouse] DROP CONSTRAINT [Warehouse_pkey];
ALTER TABLE [dbo].[Warehouse] ALTER COLUMN [WarehouseId] NVARCHAR(1000) NOT NULL;
ALTER TABLE [dbo].[Warehouse] ADD CONSTRAINT Warehouse_pkey PRIMARY KEY CLUSTERED ([WarehouseId]);

-- AddForeignKey
ALTER TABLE [dbo].[UserWarehouse] ADD CONSTRAINT [UserWarehouse_UserId_fkey] FOREIGN KEY ([UserId]) REFERENCES [dbo].[User]([UserId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[UserWarehouse] ADD CONSTRAINT [UserWarehouse_WarehouseId_fkey] FOREIGN KEY ([WarehouseId]) REFERENCES [dbo].[Warehouse]([WarehouseId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_WarehouseId_fkey] FOREIGN KEY ([WarehouseId]) REFERENCES [dbo].[Warehouse]([WarehouseId]) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_ReceivedByUserId_fkey] FOREIGN KEY ([ReceivedByUserId]) REFERENCES [dbo].[User]([UserId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_AssetId_fkey] FOREIGN KEY ([AssetId]) REFERENCES [dbo].[Asset]([AssetId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_VendorId_fkey] FOREIGN KEY ([VendorId]) REFERENCES [dbo].[Vendor]([VendorId]) ON DELETE NO ACTION ON UPDATE CASCADE;

COMMIT TRAN;

END TRY
BEGIN CATCH

IF @@TRANCOUNT > 0
BEGIN
    ROLLBACK TRAN;
END;
THROW

END CATCH
