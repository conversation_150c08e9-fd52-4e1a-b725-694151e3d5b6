/*
  Warnings:

  - The primary key for the `Asset` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `AssetId` on the `Asset` table. The data in that column could be lost. The data in that column will be cast from `NVarChar(1000)` to `UniqueIdentifier`.
  - The primary key for the `Item` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `ItemId` on the `Item` table. The data in that column could be lost. The data in that column will be cast from `NVarChar(1000)` to `UniqueIdentifier`.
  - You are about to alter the column `WarehouseId` on the `Item` table. The data in that column could be lost. The data in that column will be cast from `NVarChar(1000)` to `UniqueIdentifier`.
  - You are about to alter the column `ReceivedByUserId` on the `Item` table. The data in that column could be lost. The data in that column will be cast from `NVarChar(1000)` to `UniqueIdentifier`.
  - You are about to alter the column `AssetId` on the `Item` table. The data in that column could be lost. The data in that column will be cast from `NVarChar(1000)` to `UniqueIdentifier`.
  - You are about to alter the column `VendorId` on the `Item` table. The data in that column could be lost. The data in that column will be cast from `NVarChar(1000)` to `UniqueIdentifier`.
  - The primary key for the `User` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `UserId` on the `User` table. The data in that column could be lost. The data in that column will be cast from `NVarChar(1000)` to `UniqueIdentifier`.
  - The primary key for the `UserWarehouse` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `UserWarehouseId` on the `UserWarehouse` table. The data in that column could be lost. The data in that column will be cast from `NVarChar(1000)` to `UniqueIdentifier`.
  - You are about to alter the column `UserId` on the `UserWarehouse` table. The data in that column could be lost. The data in that column will be cast from `NVarChar(1000)` to `UniqueIdentifier`.
  - You are about to alter the column `WarehouseId` on the `UserWarehouse` table. The data in that column could be lost. The data in that column will be cast from `NVarChar(1000)` to `UniqueIdentifier`.
  - The primary key for the `Vendor` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `VendorId` on the `Vendor` table. The data in that column could be lost. The data in that column will be cast from `NVarChar(1000)` to `UniqueIdentifier`.
  - The primary key for the `Vessel` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `VesselId` on the `Vessel` table. The data in that column could be lost. The data in that column will be cast from `NVarChar(1000)` to `UniqueIdentifier`.
  - The primary key for the `Warehouse` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `WarehouseId` on the `Warehouse` table. The data in that column could be lost. The data in that column will be cast from `NVarChar(1000)` to `UniqueIdentifier`.

*/
BEGIN TRY

BEGIN TRAN;

-- DropForeignKey
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_AssetId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_ReceivedByUserId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_VendorId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_WarehouseId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[UserWarehouse] DROP CONSTRAINT [UserWarehouse_UserId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[UserWarehouse] DROP CONSTRAINT [UserWarehouse_WarehouseId_fkey];

-- AlterTable
ALTER TABLE [dbo].[Asset] DROP CONSTRAINT [Asset_pkey];
ALTER TABLE [dbo].[Asset] ALTER COLUMN [AssetId] UNIQUEIDENTIFIER NOT NULL;
ALTER TABLE [dbo].[Asset] ADD CONSTRAINT Asset_pkey PRIMARY KEY CLUSTERED ([AssetId]), CONSTRAINT [Asset_AssetId_df] DEFAULT (newid()) FOR [AssetId];

-- AlterTable
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_pkey];
ALTER TABLE [dbo].[Item] ALTER COLUMN [ItemId] UNIQUEIDENTIFIER NOT NULL;
ALTER TABLE [dbo].[Item] ALTER COLUMN [WarehouseId] UNIQUEIDENTIFIER NULL;
ALTER TABLE [dbo].[Item] ALTER COLUMN [ReceivedByUserId] UNIQUEIDENTIFIER NOT NULL;
ALTER TABLE [dbo].[Item] ALTER COLUMN [AssetId] UNIQUEIDENTIFIER NOT NULL;
ALTER TABLE [dbo].[Item] ALTER COLUMN [VendorId] UNIQUEIDENTIFIER NOT NULL;
ALTER TABLE [dbo].[Item] ADD CONSTRAINT Item_pkey PRIMARY KEY CLUSTERED ([ItemId]), CONSTRAINT [Item_ItemId_df] DEFAULT (newid()) FOR [ItemId];

-- AlterTable
ALTER TABLE [dbo].[User] DROP CONSTRAINT [User_pkey];
ALTER TABLE [dbo].[User] ALTER COLUMN [UserId] UNIQUEIDENTIFIER NOT NULL;
ALTER TABLE [dbo].[User] ADD CONSTRAINT User_pkey PRIMARY KEY CLUSTERED ([UserId]), CONSTRAINT [User_UserId_df] DEFAULT (newid()) FOR [UserId];

-- AlterTable
ALTER TABLE [dbo].[UserWarehouse] DROP CONSTRAINT [UserWarehouse_pkey];
ALTER TABLE [dbo].[UserWarehouse] ALTER COLUMN [UserWarehouseId] UNIQUEIDENTIFIER NOT NULL;
ALTER TABLE [dbo].[UserWarehouse] ALTER COLUMN [UserId] UNIQUEIDENTIFIER NOT NULL;
ALTER TABLE [dbo].[UserWarehouse] ALTER COLUMN [WarehouseId] UNIQUEIDENTIFIER NOT NULL;
ALTER TABLE [dbo].[UserWarehouse] ADD CONSTRAINT UserWarehouse_pkey PRIMARY KEY CLUSTERED ([UserWarehouseId]), CONSTRAINT [UserWarehouse_UserWarehouseId_df] DEFAULT (newid()) FOR [UserWarehouseId];

-- AlterTable
ALTER TABLE [dbo].[Vendor] DROP CONSTRAINT [Vendor_pkey];
ALTER TABLE [dbo].[Vendor] ALTER COLUMN [VendorId] UNIQUEIDENTIFIER NOT NULL;
ALTER TABLE [dbo].[Vendor] ADD CONSTRAINT Vendor_pkey PRIMARY KEY CLUSTERED ([VendorId]), CONSTRAINT [Vendor_VendorId_df] DEFAULT (newid()) FOR [VendorId];

-- AlterTable
ALTER TABLE [dbo].[Vessel] DROP CONSTRAINT [Vessel_pkey];
ALTER TABLE [dbo].[Vessel] ALTER COLUMN [VesselId] UNIQUEIDENTIFIER NOT NULL;
ALTER TABLE [dbo].[Vessel] ADD CONSTRAINT Vessel_pkey PRIMARY KEY CLUSTERED ([VesselId]), CONSTRAINT [Vessel_VesselId_df] DEFAULT (newid()) FOR [VesselId];

-- AlterTable
ALTER TABLE [dbo].[Warehouse] DROP CONSTRAINT [Warehouse_pkey];
ALTER TABLE [dbo].[Warehouse] ALTER COLUMN [WarehouseId] UNIQUEIDENTIFIER NOT NULL;
ALTER TABLE [dbo].[Warehouse] ADD CONSTRAINT Warehouse_pkey PRIMARY KEY CLUSTERED ([WarehouseId]), CONSTRAINT [Warehouse_WarehouseId_df] DEFAULT (newid()) FOR [WarehouseId];

-- AddForeignKey
ALTER TABLE [dbo].[UserWarehouse] ADD CONSTRAINT [UserWarehouse_UserId_fkey] FOREIGN KEY ([UserId]) REFERENCES [dbo].[User]([UserId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[UserWarehouse] ADD CONSTRAINT [UserWarehouse_WarehouseId_fkey] FOREIGN KEY ([WarehouseId]) REFERENCES [dbo].[Warehouse]([WarehouseId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_WarehouseId_fkey] FOREIGN KEY ([WarehouseId]) REFERENCES [dbo].[Warehouse]([WarehouseId]) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_ReceivedByUserId_fkey] FOREIGN KEY ([ReceivedByUserId]) REFERENCES [dbo].[User]([UserId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_AssetId_fkey] FOREIGN KEY ([AssetId]) REFERENCES [dbo].[Asset]([AssetId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_VendorId_fkey] FOREIGN KEY ([VendorId]) REFERENCES [dbo].[Vendor]([VendorId]) ON DELETE NO ACTION ON UPDATE CASCADE;

COMMIT TRAN;

END TRY
BEGIN CATCH

IF @@TRANCOUNT > 0
BEGIN
    ROLLBACK TRAN;
END;
THROW

END CATCH
