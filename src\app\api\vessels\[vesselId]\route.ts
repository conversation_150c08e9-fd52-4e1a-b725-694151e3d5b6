import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET one vessel
export async function GET(
  _request: NextRequest,
  context: { params: Promise<{ vesselId: string }> }
) {
  try {
    const { vesselId } = await context.params;
    const vessel = await prisma.vessel.findUnique({
      where: { VesselId: vesselId, IsDeleted: false },
    });
    if (!vessel) {
      return NextResponse.json({ error: 'Not found' }, { status: 404 });
    }
    return NextResponse.json(vessel);
  } catch {
    return NextResponse.json(
      { error: 'Error fetching vessel' },
      { status: 500 }
    );
  }
}

// PUT update one vessel
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ vesselId: string }> }
) {
  try {
    const { vesselId } = await context.params;
    const data = await request.json();

    await prisma.vessel.update({
      where: { VesselId: vesselId },
      data,
    });
    return NextResponse.json({ message: 'Vessel updated' });
  } catch {
    return NextResponse.json(
      { error: 'Error updating vessel' },
      { status: 500 }
    );
  }
}
