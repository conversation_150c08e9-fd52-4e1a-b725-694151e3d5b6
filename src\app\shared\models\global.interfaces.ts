import { PackageType } from './global.enums';

export interface IItem {
  ItemId: string;
  WarehouseId?: string | null;
  ReceivedByUserId: string;
  AssetId: string;
  VendorId: string;
  VesselId?: string | null;
  ReceiptNumber: string;
  ReceiptDateTime: Date;
  SailingDate?: Date | null;
  PONumber?: string | null;
  SupplierDeliveryNoteNumber?: string | null;
  NumberOfPackages: number;
  NumberOfLineItems?: number | null;
  packageType: PackageType | PackageType[];
  SAPDelivery?: string | null;
  DescriptionOfGoods?: string | null;
  IsQuarantined: boolean;
  LoadListItem: boolean;
  CCU?: string | null;
  Manifest?: string | null;
  IsDeleted: boolean;
  DateClosed?: Date | null;
  NCRNumber?: string | null;
  DescriptionOfNonConformance?: string | null;
  Status?: string | null;

  // Relations
  Warehouse?: IWarehouse;
  ReceivedUser: IUser;
  Asset: IAsset;
  Vendor: IVendor;
  Vessel?: IVessel;
}

// @todo check whether outsourcing these interfaces to a more appropriate location makes sense (since they can be for both API and client)
export interface IWarehouse {
  WarehouseId: string;
  Name: string;
}

export interface IUser {
  UserId: string;
  Name: string;
}

export interface IAsset {
  AssetId: string;
  Name: string;
}

export interface IVendor {
  VendorId: string;
  Name: string;
}

export interface IVessel {
  VesselId: string;
  Name: string;
}
