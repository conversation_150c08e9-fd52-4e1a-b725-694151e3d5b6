import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { getToken } from 'next-auth/jwt';
import { Role } from '@/app/shared/models/global.enums';

// GET: get a specific warehouse's details, check assignment if not admin
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ WarehouseId: string }> }
) {
  try {
    const { WarehouseId } = await context.params; // Await the promise

    // Get user information from JWT token
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    // If no token, check cookies as fallback
    const cookieStore = await cookies();
    let userEmail: string | undefined;

    if (!token) {
      try {
        userEmail = cookieStore.get('userEmail')?.value;
      } catch (error) {
        console.error('Error accessing cookies:', error);
      }
    } else {
      // Prefer token data if available
      userEmail = token.email as string;
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'Unauthorized: User not authenticated' },
        { status: 401 }
      );
    }

    // Fetch user record via email
    const userRecord = await prisma.user.findUnique({
      where: { Email: userEmail },
    });

    if (!userRecord) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // If user is system_admin, they can access any warehouse
    if (userRecord.Role === Role.SystemAdmin) {
      const warehouse = await prisma.warehouse.findUnique({
        where: {
          WarehouseId,
          IsDeleted: false,
        },
        include: {
          Users: true,
        },
      });

      if (!warehouse) {
        return NextResponse.json(
          { error: 'Warehouse not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(warehouse);
    }

    // For admin and other roles, only allow access to assigned warehouses
    const warehouse = await prisma.warehouse.findFirst({
      where: {
        WarehouseId,
        IsDeleted: false,
        Users: { some: { UserId: userRecord.UserId } },
      },
      include: {
        Users: {
          where: { UserId: userRecord.UserId },
        },
      },
    });

    if (!warehouse) {
      return NextResponse.json({ error: 'Not allowed' }, { status: 403 });
    }

    return NextResponse.json(warehouse);
  } catch (error: unknown) {
    console.error('Error fetching warehouse:', error);

    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// PUT: update a specific warehouse
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ WarehouseId: string }> }
) {
  try {
    const { WarehouseId } = await context.params; // Await the promise
    const { Name } = await request.json();

    if (!Name) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    // Get user information from JWT token
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    // If no token, check cookies as fallback
    const cookieStore = await cookies();
    let userEmail: string | undefined;

    if (!token) {
      try {
        userEmail = cookieStore.get('userEmail')?.value;
      } catch (error) {
        console.error('Error accessing cookies:', error);
      }
    } else {
      // Prefer token data if available
      userEmail = token.email as string;
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'Unauthorized: User not authenticated' },
        { status: 401 }
      );
    }

    // Fetch user record via email
    const userRecord = await prisma.user.findUnique({
      where: { Email: userEmail },
    });

    if (!userRecord) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if warehouse exists
    const existingWarehouse = await prisma.warehouse.findUnique({
      where: {
        WarehouseId,
        IsDeleted: false,
      },
    });

    if (!existingWarehouse) {
      return NextResponse.json(
        { error: 'Warehouse not found' },
        { status: 404 }
      );
    }

    // Only system_admin can update warehouses
    if (userRecord.Role !== Role.SystemAdmin) {
      return NextResponse.json(
        { error: 'Only System Admins can update warehouses' },
        { status: 403 }
      );
    }
    
    // Proceed with update - system admin can update any warehouse
    const updatedWarehouse = await prisma.warehouse.update({
      where: { WarehouseId },
      data: { Name },
    });

    return NextResponse.json(updatedWarehouse);
  } catch (error: unknown) {
    console.error('Error updating warehouse:', error);

    if (
      typeof error === 'object' &&
      error !== null &&
      'code' in error &&
      error.code === 'P2025'
    ) {
      return NextResponse.json(
        { error: 'Warehouse not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// DELETE: remove a specific warehouse
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ WarehouseId: string }> }
) {
  try {
    const { WarehouseId } = await context.params; // Await the promise

    // Get user information from JWT token
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    // If no token, check cookies as fallback
    const cookieStore = await cookies();
    let userEmail: string | undefined;

    if (!token) {
      try {
        userEmail = cookieStore.get('userEmail')?.value;
      } catch (error) {
        console.error('Error accessing cookies:', error);
      }
    } else {
      // Prefer token data if available
      userEmail = token.email as string;
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'Unauthorized: User not authenticated' },
        { status: 401 }
      );
    }

    // Fetch user record via email
    const userRecord = await prisma.user.findUnique({
      where: { Email: userEmail },
    });

    if (!userRecord) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Only system_admins can delete warehouses
    if (userRecord.Role !== 'system_admin') {
      return NextResponse.json(
        { error: 'Only system administrators can delete warehouses' },
        { status: 403 }
      );
    }

    // Check if warehouse exists
    const existingWarehouse = await prisma.warehouse.findUnique({
      where: { WarehouseId },
    });

    if (!existingWarehouse) {
      return NextResponse.json(
        { error: 'Warehouse not found' },
        { status: 404 }
      );
    }

    // Soft delete the warehouse instead of hard delete
    await prisma.warehouse.update({
      where: { WarehouseId },
      data: { IsDeleted: true },
    });

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    console.error('Error deleting warehouse:', error);

    if (
      typeof error === 'object' &&
      error !== null &&
      'code' in error &&
      error.code === 'P2025'
    ) {
      return NextResponse.json(
        { error: 'Warehouse not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
