import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getToken } from 'next-auth/jwt';

// GET: Get warehouse assignments for a specific user
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    // Verify authentication
    const token = await getToken({
      req,
      secret: process.env.NEXTAUTH_SECRET,
    });

    if (!token || !token.email) {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      );
    }

    // Get the user from the database
    const requestUser = await prisma.user.findFirst({
      where: {
        Email: token.email,
        IsDeleted: false,
      },
    });

    if (!requestUser) {
      return NextResponse.json(
        { error: 'Unauthorized: User not found' },
        { status: 401 }
      );
    }

    // Await the params to extract userId
    const { userId } = await context.params;

    // Only allow admins, system_admins, or the user themselves to access their warehouse assignments
    if (
      requestUser.Role !== 'admin' &&
      requestUser.Role !== 'system_admin' &&
      requestUser.UserId !== userId
    ) {
      return NextResponse.json(
        {
          error:
            'Unauthorized: You can only view your own warehouse assignments unless you are an administrator',
        },
        { status: 403 }
      );
    }

    // Get the user's warehouse assignments using the awaited userId
    const userWarehouseAssignments = await prisma.userWarehouse.findMany({
      where: { UserId: userId },
      include: {
        Warehouse: true,
      },
    });

    return NextResponse.json(userWarehouseAssignments, { status: 200 });
  } catch (error) {
    console.error('Error fetching user warehouse assignments:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
