'use client';
import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { useWarehouseStore } from '@/store';
import { useAuthStore } from '@/store';

// Import Dropdown with dynamic import to prevent SSR issues
const Dropdown = dynamic(
  () => import('primereact/dropdown').then((mod) => mod.Dropdown),
  { ssr: false }
);

export default function WarehouseDropdown() {
  const warehouses = useWarehouseStore((state) => state.warehouses);
  const selectedWarehouse = useWarehouseStore(
    (state) => state.selectedWarehouse
  );
  const setSelectedWarehouse = useWarehouseStore(
    (state) => state.setSelectedWarehouse
  );
  const fetchAndSetWarehouses = useWarehouseStore(
    (state) => state.fetchAndSetWarehouses
  );
  const user = useAuthStore((state) => state.user);
  const isUserLoading = useAuthStore((state) => state.isUserLoading);

  const [dropdownValue, setDropdownValue] = useState(selectedWarehouse);
  const [isMounted, setIsMounted] = useState(false);

  // Handle client-side only rendering
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    setDropdownValue(selectedWarehouse);
  }, [selectedWarehouse]);

  // Fetch warehouses only when user is available with email
  useEffect(() => {
    // Only fetch warehouses if user exists
    if (user && user.Email && !isUserLoading && warehouses.length === 0) {
      fetchAndSetWarehouses();
    }
  }, [user, isUserLoading, warehouses.length, fetchAndSetWarehouses]);

  useEffect(() => {
    // Skip if no user or no warehouses
    if (!user || !user.Email || warehouses.length === 0) return;

    // Only update selection if there's no valid selection
    const isAdminOrSystemAdmin =
      user.Role === 'admin' || user.Role === 'system_admin';
    const validWarehouseIds = isAdminOrSystemAdmin
      ? ['all', ...warehouses.map((w) => w.WarehouseId)]
      : warehouses.map((w) => w.WarehouseId);

    if (
      !selectedWarehouse ||
      !validWarehouseIds.includes(selectedWarehouse.WarehouseId)
    ) {
      // Auto-select "All Warehouses" for admin and system_admin users if no selection exists
      if (user.Role === 'admin' || user.Role === 'system_admin') {
        setSelectedWarehouse({ WarehouseId: 'all', Name: 'All Warehouses' });
      } else {
        const defaultWarehouse = warehouses[0];
        setSelectedWarehouse(defaultWarehouse);
      }
    }
  }, [warehouses, user, selectedWarehouse, setSelectedWarehouse]);

  // Don't render anything during SSR
  if (!isMounted) {
    return <div className="flex justify-end mr-6 mt-6"></div>;
  }

  // Return nothing when user is loading or logged out
  if (isUserLoading) return null;
  if (!user || !user.Email) return null;

  // Show loading state if warehouses are not loaded yet
  if (!warehouses || warehouses.length === 0) {
    return (
      <div className="flex justify-end mr-6 mt-6">
        <Dropdown loading disabled className="md:w-14rem" />
      </div>
    );
  }

  // Get the options for the dropdown
  const options =
    user.Role === 'admin' || user.Role === 'system_admin'
      ? [{ WarehouseId: 'all', Name: 'All Warehouses' }, ...warehouses]
      : warehouses;

  return (
    <div className="flex justify-end mr-6 mt-6">
      <Dropdown
        value={dropdownValue}
        onChange={(e) => {
          setDropdownValue(e.value);
          setSelectedWarehouse(e.value);
        }}
        options={options}
        optionLabel="Name"
        placeholder="Select a Warehouse"
        className="md:w-14rem"
      />
    </div>
  );
}
