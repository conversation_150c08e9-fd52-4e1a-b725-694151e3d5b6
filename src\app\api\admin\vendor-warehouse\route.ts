import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { getToken } from 'next-auth/jwt';
import { Role } from '@/app/shared/models/global.enums';

// GET: Get all vendor-warehouse relationships
export async function GET(request: NextRequest) {
  try {
    // Get user information from JWT token
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    // If no token, check cookies as fallback
    const cookieStore = await cookies();
    let userEmail: string | undefined;

    if (!token) {
      try {
        userEmail = cookieStore.get('userEmail')?.value;
      } catch (error) {
        console.error('Error accessing cookies:', error);
      }
    } else {
      // Prefer token data if available
      userEmail = token.email as string;
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'Unauthorized: User not authenticated' },
        { status: 401 }
      );
    }

    // Always fetch the user from the database to get the most up-to-date role
    const userRecord = await prisma.user.findUnique({
      where: { Email: userEmail },
    });

    if (!userRecord) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Use the database role as the source of truth
    const isSystemAdmin = userRecord.Role === Role.SystemAdmin;
    const isAdmin = userRecord.Role === Role.Admin;

    // If user is system_admin, return all vendor-warehouse relationships
    if (isSystemAdmin) {
      const vendorWarehouses = await prisma.vendorWarehouse.findMany({
        include: {
          Vendor: true,
          Warehouse: true,
        },
      });
      return NextResponse.json(vendorWarehouses, { status: 200 });
    }

    // If user is admin, return only vendor-warehouse relationships for warehouses they have access to
    if (isAdmin) {
      // Get warehouses assigned to this admin
      const userWarehouses = await prisma.userWarehouse.findMany({
        where: { UserId: userRecord.UserId },
        select: { WarehouseId: true },
      });

      const warehouseIds = userWarehouses.map(
        (uw: { WarehouseId: string }) => uw.WarehouseId
      );

      const vendorWarehouses = await prisma.vendorWarehouse.findMany({
        where: {
          WarehouseId: { in: warehouseIds },
        },
        include: {
          Vendor: true,
          Warehouse: true,
        },
      });

      return NextResponse.json(vendorWarehouses, { status: 200 });
    }

    // For non-admin users, return 403
    return NextResponse.json(
      { error: 'Forbidden: Only admins can access this resource' },
      { status: 403 }
    );
  } catch (error) {
    console.error('Error fetching vendor-warehouse relationships:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST: Create a new vendor-warehouse relationship
export async function POST(request: NextRequest) {
  try {
    // Get user information from JWT token
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    // If no token, check cookies as fallback
    const cookieStore = await cookies();
    let userEmail: string | undefined;

    if (!token) {
      try {
        userEmail = cookieStore.get('userEmail')?.value;
      } catch (error) {
        console.error('Error accessing cookies:', error);
      }
    } else {
      // Prefer token data if available
      userEmail = token.email as string;
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'Unauthorized: User not authenticated' },
        { status: 401 }
      );
    }

    // Always fetch the user from the database to get the most up-to-date role
    const userRecord = await prisma.user.findUnique({
      where: { Email: userEmail },
    });

    if (!userRecord) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Use the DB role as the source of truth
    const isSystemAdmin = userRecord.Role === 'system_admin';
    const isAdmin = userRecord.Role === 'admin';

    if (!isSystemAdmin && !isAdmin) {
      return NextResponse.json(
        {
          error:
            'Unauthorized: Only admins can create vendor-warehouse relationships',
        },
        { status: 403 }
      );
    }

    const { VendorId, WarehouseId } = await request.json();

    if (!VendorId || !WarehouseId) {
      return NextResponse.json(
        { error: 'VendorId and WarehouseId are required' },
        { status: 400 }
      );
    }

    // If admin (not system_admin), check if they have access to this warehouse
    if (isAdmin && !isSystemAdmin) {
      const userWarehouse = await prisma.userWarehouse.findFirst({
        where: {
          UserId: userRecord.UserId,
          WarehouseId: WarehouseId,
        },
      });

      if (!userWarehouse) {
        return NextResponse.json(
          { error: 'Unauthorized: You do not have access to this warehouse' },
          { status: 403 }
        );
      }
    }

    // Check if the relationship already exists
    const existingRelationship = await prisma.vendorWarehouse.findFirst({
      where: {
        VendorId,
        WarehouseId,
      },
    });

    if (existingRelationship) {
      return NextResponse.json(
        { error: 'Relationship already exists' },
        { status: 409 }
      );
    }

    const newVendorWarehouse = await prisma.vendorWarehouse.create({
      data: {
        VendorId,
        WarehouseId,
      },
    });

    return NextResponse.json(newVendorWarehouse, { status: 201 });
  } catch (error) {
    console.error('Error creating vendor-warehouse relationship:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE: Delete vendor-warehouse relationships
export async function DELETE(request: NextRequest) {
  try {
    // Get user information from JWT token
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    // If no token, check cookies as fallback
    const cookieStore = await cookies();
    let userEmail: string | undefined;

    if (!token) {
      try {
        userEmail = cookieStore.get('userEmail')?.value;
      } catch (error) {
        console.error('Error accessing cookies:', error);
      }
    } else {
      // Prefer token data if available
      userEmail = token.email as string;
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'Unauthorized: User not authenticated' },
        { status: 401 }
      );
    }

    // Always fetch the user from the database to get the most up-to-date role
    const userRecord = await prisma.user.findUnique({
      where: { Email: userEmail },
    });

    if (!userRecord) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Use the DB role as the source of truth
    const isSystemAdmin = userRecord.Role === 'system_admin';
    const isAdmin = userRecord.Role === 'admin';

    if (!isSystemAdmin && !isAdmin) {
      return NextResponse.json(
        {
          error:
            'Unauthorized: Only admins can delete vendor-warehouse relationships',
        },
        { status: 403 }
      );
    }

    const { VendorId, WarehouseId } = await request.json();

    if (!VendorId || !WarehouseId) {
      return NextResponse.json(
        { error: 'VendorId and WarehouseId are required' },
        { status: 400 }
      );
    }

    // If admin (not system_admin), check if they have access to this warehouse
    if (isAdmin && !isSystemAdmin) {
      const userWarehouse = await prisma.userWarehouse.findFirst({
        where: {
          UserId: userRecord.UserId,
          WarehouseId: WarehouseId,
        },
      });

      if (!userWarehouse) {
        return NextResponse.json(
          { error: 'Unauthorized: You do not have access to this warehouse' },
          { status: 403 }
        );
      }
    }

    // Delete the relationship
    await prisma.vendorWarehouse.deleteMany({
      where: {
        VendorId,
        WarehouseId,
      },
    });

    return NextResponse.json({ success: true }, { status: 200 });
  } catch (error) {
    console.error('Error deleting vendor-warehouse relationship:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
