import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET one vendor
export async function GET(
  _request: NextRequest,
  context: { params: Promise<{ vendorId: string }> }
) {
  try {
    const { vendorId } = await context.params;
    const vendor = await prisma.vendor.findUnique({
      where: { VendorId: vendorId, IsDeleted: false },
    });
    if (!vendor) {
      return NextResponse.json({ error: 'Not found' }, { status: 404 });
    }
    return NextResponse.json(vendor);
  } catch {
    return NextResponse.json(
      { error: 'Error fetching vendor' },
      { status: 500 }
    );
  }
}

// PUT update one vendor or soft-delete
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ vendorId: string }> }
) {
  try {
    const { vendorId } = await context.params;
    const data = await request.json();
    await prisma.vendor.update({
      where: { VendorId: vendorId },
      data,
    });
    return NextResponse.json({ message: 'Vendor updated' });
  } catch {
    return NextResponse.json(
      { error: 'Error updating vendor' },
      { status: 500 }
    );
  }
}
