import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const vendors = await prisma.vendor.findMany({
      where: { IsDeleted: false },
      orderBy: { Name: 'asc' },
    });
    return NextResponse.json(vendors);
  } catch (error) {
    return NextResponse.json(
      { error: 'Error listing vendors' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { Name } = await request.json();
    const newVendor = await prisma.vendor.create({ data: { Name } });
    return NextResponse.json(newVendor, { status: 201 });
  } catch (error) {
    return NextResponse.json(
      { error: 'Error creating vendor' },
      { status: 500 }
    );
  }
}
