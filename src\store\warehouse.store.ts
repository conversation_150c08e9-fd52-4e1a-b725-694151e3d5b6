import { create } from 'zustand';
import { IWarehouse } from '@/app/shared/models/global.interfaces';
import { fetchWarehouses } from '@/app/(views)/admin/admin.services';

interface WarehouseStore {
  selectedWarehouse: IWarehouse | null;
  setSelectedWarehouse: (warehouse: IWarehouse | null) => void;
  warehouses: IWarehouse[];
  fetchAndSetWarehouses: () => Promise<void>;
  retriggerFetchWarehouses: () => void;
  clearWarehouseSelection: () => void;
}

export const useWarehouseStore = create<WarehouseStore>()((set, get) => ({
  selectedWarehouse: null,
  warehouses: [],

  setSelectedWarehouse: (warehouse) =>
    set((state) => ({
      ...state,
      selectedWarehouse: warehouse ?? state.selectedWarehouse, // Keep the current selection if warehouse is null
    })),

  fetchAndSetWarehouses: async () => {
    try {
      const warehouses = await fetchWarehouses();
      set((state) => ({
        ...state,
        warehouses,
      }));
    } catch (error) {
      console.error('Failed to fetch and set warehouses:', error);
    }
  },

  retriggerFetchWarehouses: () => {
    get().fetchAndSetWarehouses();
  },

  // Clears warehouse selection on refresh
  clearWarehouseSelection: () => {
    set({ selectedWarehouse: null, warehouses: [] });
  },
}));
