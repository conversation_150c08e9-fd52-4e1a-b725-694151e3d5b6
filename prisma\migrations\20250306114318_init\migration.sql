BEGIN TRY

BEGIN TRAN;

-- CreateTable
CREATE TABLE [dbo].[Asset] (
    [AssetId] uniqueidentifier NOT NULL,
    [Name] NVARCHAR(1000) NOT NULL,
    CONSTRAINT [Asset_pkey] PRIMARY KEY CLUSTERED ([AssetId])
);

-- CreateTable
CREATE TABLE [dbo].[Vendor] (
    [VendorId] uniqueidentifier NOT NULL,
    [Name] NVARCHAR(1000) NOT NULL,
    CONSTRAINT [Vendor_pkey] PRIMARY KEY CLUSTERED ([VendorId])
);

-- CreateTable
CREATE TABLE [dbo].[Warehouse] (
    [WarehouseId] uniqueidentifier NOT NULL,
    [Name] NVARCHAR(1000) NOT NULL,
    CONSTRAINT [Warehouse_pkey] PRIMARY KEY CLUSTERED ([WarehouseId])
);

-- CreateTable
CREATE TABLE [dbo].[User] (
    [UserId] uniqueidentifier NOT NULL,
    [Name] NVARCHAR(1000) NOT NULL,
    [Email] NVARCHAR(1000) NOT NULL,
    [Role] NVARCHAR(1000) NOT NULL,
    CONSTRAINT [User_pkey] PRIMARY KEY CLUSTERED ([UserId]),
    CONSTRAINT [User_Email_key] UNIQUE NONCLUSTERED ([Email])
);

-- CreateTable
CREATE TABLE [dbo].[UserWarehouse] (
    [UserWarehouseId] uniqueidentifier NOT NULL,
    [UserId] uniqueidentifier NOT NULL,
    [WarehouseId] uniqueidentifier NOT NULL,
    CONSTRAINT [UserWarehouse_pkey] PRIMARY KEY CLUSTERED ([UserWarehouseId])
);

-- CreateTable
CREATE TABLE [dbo].[Vessel] (
    [VesselId] uniqueidentifier NOT NULL,
    [Name] NVARCHAR(1000) NOT NULL,
    CONSTRAINT [Vessel_pkey] PRIMARY KEY CLUSTERED ([VesselId])
);

-- CreateTable
CREATE TABLE [dbo].[Item] (
    [ItemId] uniqueidentifier NOT NULL,
    [WarehouseId] uniqueidentifier,
    [ReceivedByUserId] uniqueidentifier NOT NULL,
    [AssetId] uniqueidentifier NOT NULL,
    [VendorId] uniqueidentifier NOT NULL,
    [ReceiptNumber] NVARCHAR(1000) NOT NULL,
    [ReceiptDateTime] DATETIME2 NOT NULL,
    [SailingDate] DATETIME2 NOT NULL,
    [PONumber] NVARCHAR(1000),
    [SupplierDeliveryNoteNumber] NVARCHAR(1000),
    [NumberOfPackages] INT NOT NULL,
    [NumberOfLineItems] INT,
    [packageType] NVARCHAR(1000) NOT NULL,
    [SAPDelivery] NVARCHAR(1000),
    [DescriptionOfGoods] NVARCHAR(1000),
    [IsQuarantined] BIT NOT NULL CONSTRAINT [Item_IsQuarantined_df] DEFAULT 0,
    [LoadListItem] BIT NOT NULL CONSTRAINT [Item_LoadListItem_df] DEFAULT 0,
    [CCU] NVARCHAR(1000),
    [Manifest] NVARCHAR(1000),
    CONSTRAINT [Item_pkey] PRIMARY KEY CLUSTERED ([ItemId]),
    CONSTRAINT [Item_ReceiptNumber_key] UNIQUE NONCLUSTERED ([ReceiptNumber])
);

-- AddForeignKey
ALTER TABLE [dbo].[UserWarehouse] ADD CONSTRAINT [UserWarehouse_UserId_fkey] FOREIGN KEY ([UserId]) REFERENCES [dbo].[User]([UserId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[UserWarehouse] ADD CONSTRAINT [UserWarehouse_WarehouseId_fkey] FOREIGN KEY ([WarehouseId]) REFERENCES [dbo].[Warehouse]([WarehouseId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_WarehouseId_fkey] FOREIGN KEY ([WarehouseId]) REFERENCES [dbo].[Warehouse]([WarehouseId]) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_ReceivedByUserId_fkey] FOREIGN KEY ([ReceivedByUserId]) REFERENCES [dbo].[User]([UserId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_AssetId_fkey] FOREIGN KEY ([AssetId]) REFERENCES [dbo].[Asset]([AssetId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_VendorId_fkey] FOREIGN KEY ([VendorId]) REFERENCES [dbo].[Vendor]([VendorId]) ON DELETE NO ACTION ON UPDATE CASCADE;

COMMIT TRAN;

END TRY
BEGIN CATCH

IF @@TRANCOUNT > 0
BEGIN
    ROLLBACK TRAN;
END;
THROW

END CATCH
