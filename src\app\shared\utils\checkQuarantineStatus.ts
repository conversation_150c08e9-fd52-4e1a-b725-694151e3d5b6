import { QuarantineStatus } from '@/app/shared/models/global.enums';
import { IItem } from '../models/global.interfaces';

function checkQuarantineStatus(data: Partial<IItem>): Partial<IItem> {
  if (data.IsQuarantined && !data.Status) {
    data.Status = QuarantineStatus.Pending;
  } else if (!data.IsQuarantined && data.Status) {
    data.Status = QuarantineStatus.Resolved;
  }
  return data;
}

export default checkQuarantineStatus;
