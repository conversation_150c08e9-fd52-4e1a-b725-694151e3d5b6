'use client';

import useS<PERSON> from 'swr';
import { useRef, useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import ReusableToast from '@/app/shared/components/ReusableToast';
import { But<PERSON> } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { textEditor } from '../../receipts/utils/editors.utils';
import { openDeleteConfirmDialog } from '../../receipts/components/utils/ReceiptsDialogsSettings';
import ReusableConfirmDialog from '@/app/shared/components/ReusableConfirmDialog';
import { fetchVessels, createVessel, updateVessel } from '../admin.services';
import { useAuthStore } from '@/store';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash } from '@fortawesome/free-solid-svg-icons';

export default function VesselsManagement() {
  const {
    data: vessels,
    error,
    mutate,
  } = useSWR('/admin/vessels', fetchVessels);
  const [newVessel, setNewVessel] = useState({ Name: '' });
  const [editingRowId, setEditingRowId] = useState<string | null>(null);
  const toastRef = useRef<any>(null);

  const isUserLoading = useAuthStore((state) => state.isUserLoading);
  if (isUserLoading) return <p>Loading vessels...</p>;

  const onRowEditInit = (e: any) => setEditingRowId(e.data.VesselId);
  const onRowEditCancel = () => setEditingRowId(null);

  const onRowEditComplete = async (e: any) => {
    try {
      await updateVessel(e.newData.VesselId, { Name: e.newData.Name });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Updated',
        detail: 'Vessel updated',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update vessel',
      });
    } finally {
      setEditingRowId(null);
    }
  };

  const handleCreate = async () => {
    try {
      await createVessel(newVessel);
      setNewVessel({ Name: '' });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Created',
        detail: 'Vessel created',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to create vessel',
      });
    }
  };

  const handleDelete = async (v: any) => {
    try {
      await updateVessel(v.VesselId, { IsDeleted: true }); // soft-delete
      toastRef.current?.show({
        severity: 'success',
        summary: 'Deleted',
        detail: 'Vessel deleted',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to delete vessel',
      });
    }
  };

  const confirmDelete = (rowData: any) => {
    openDeleteConfirmDialog({ onAccept: () => handleDelete(rowData) });
  };

  if (!vessels) return <p>Loading...</p>;
  if (error) return <p>Error loading vessels</p>;

  return (
    <>
      <ReusableToast ref={toastRef} />
      <h1>Vessels Management</h1>
      <div className="flex gap-2 my-2">
        <InputText
          placeholder="Name"
          value={newVessel.Name}
          onChange={(e) => setNewVessel({ Name: e.target.value })}
        />
        <Button
          disabled={!newVessel.Name}
          label="Create Vessel"
          onClick={handleCreate}
        />
      </div>

      <DataTable
        value={vessels}
        editMode="row"
        dataKey="VesselId"
        size="small"
        stripedRows
        onRowEditInit={onRowEditInit}
        onRowEditCancel={onRowEditCancel}
        onRowEditComplete={onRowEditComplete}
      >
        {/* <Column field="VesselId" header="ID" /> */}
        <Column
          field="Name"
          header="Name"
          editor={(opts) => textEditor(opts)}
        />
        <Column rowEditor headerStyle={{ width: '4rem' }} />
        <Column
          body={(rowData) => (
            <Button
              icon={<FontAwesomeIcon icon={faTrash} />}
              className="p-button-text p-button-rounded p-button-danger"
              onClick={() => confirmDelete(rowData)}
              tooltip="Delete"
              tooltipOptions={{ position: 'top' }}
            />
          )}
          headerStyle={{ width: '6rem' }}
        />
      </DataTable>

      <ReusableConfirmDialog />
    </>
  );
}
