import { prisma } from '@/lib/prisma';

export async function getNextReceiptNumber(): Promise<string> {
  let count = await prisma.item.count();
  let candidateNumber: string;
  let exists: any;
  do {
    candidateNumber = 'A' + count.toString().padStart(4, '0');
    exists = await prisma.item.findUnique({
      where: { ReceiptNumber: candidateNumber },
    });
    count++;
  } while (exists);
  return candidateNumber;
}
