'use client';

import { useState, useEffect, useRef } from 'react';
import { Panel } from 'primereact/panel';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { MultiSelect } from 'primereact/multiselect';
import useSWR from 'swr';
import { fetchWarehouses } from '@/app/(views)/admin/admin.services';
import ReusableToast from './ReusableToast';

interface WarehouseAssignmentPanelProps {
  entityId: string;
  entityType: 'asset' | 'vessel' | 'vendor';
  entityName: string;
}

export default function WarehouseAssignmentPanel({
  entityId,
  entityType,
  entityName,
}: WarehouseAssignmentPanelProps) {
  const { data: warehouses = [], error: warehousesError } = useSWR(
    '/admin/warehouses',
    fetchWarehouses
  );
  const [assignedWarehouses, setAssignedWarehouses] = useState<any[]>([]);
  const [selectedWarehouses, setSelectedWarehouses] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const toastRef = useRef<any>(null);

  // Fetch the current warehouse assignments for this entity
  useEffect(() => {
    if (entityId) {
      fetchAssignedWarehouses();
    }
  }, [entityId]);

  const fetchAssignedWarehouses = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/admin/${entityType}-warehouse?${entityType}Id=${entityId}`
      );
      if (response.ok) {
        const data = await response.json();
        setAssignedWarehouses(data);
      } else {
        console.error('Failed to fetch assigned warehouses');
      }
    } catch (error) {
      console.error('Error fetching assigned warehouses:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveAssignments = async () => {
    try {
      setLoading(true);

      // Get currently assigned warehouse IDs
      const currentAssignedIds = assignedWarehouses.map(
        (aw: any) => aw.WarehouseId
      );

      // Get selected warehouse IDs
      const selectedIds = selectedWarehouses.map((w) => w.WarehouseId);

      // Determine which warehouses to add and which to remove
      const toAdd = selectedIds.filter(
        (id) => !currentAssignedIds.includes(id)
      );
      const toRemove = currentAssignedIds.filter(
        (id) => !selectedIds.includes(id)
      );

      // Add new assignments
      for (const warehouseId of toAdd) {
        await fetch(`/api/admin/${entityType}-warehouse`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            [`${entityType.charAt(0).toUpperCase() + entityType.slice(1)}Id`]:
              entityId,
            WarehouseId: warehouseId,
          }),
        });
      }

      // Remove old assignments
      for (const warehouseId of toRemove) {
        await fetch(`/api/admin/${entityType}-warehouse`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            [`${entityType.charAt(0).toUpperCase() + entityType.slice(1)}Id`]:
              entityId,
            WarehouseId: warehouseId,
          }),
        });
      }

      // Refresh the assigned warehouses
      await fetchAssignedWarehouses();

      toastRef.current?.show({
        severity: 'success',
        summary: 'Updated',
        detail: 'Warehouse assignments updated',
      });
    } catch (error) {
      console.error('Error updating warehouse assignments:', error);
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update warehouse assignments',
      });
    } finally {
      setLoading(false);
    }
  };

  // Update the selected warehouses when the assigned warehouses change
  useEffect(() => {
    if (assignedWarehouses.length > 0 && warehouses.length > 0) {
      const assignedIds = assignedWarehouses.map((aw: any) => aw.WarehouseId);
      const selected = warehouses.filter((w: any) =>
        assignedIds.includes(w.WarehouseId)
      );
      setSelectedWarehouses(selected);
    } else {
      setSelectedWarehouses([]);
    }
  }, [assignedWarehouses, warehouses]);

  if (warehousesError) {
    return <div>Error loading warehouses</div>;
  }

  return (
    <>
      <ReusableToast ref={toastRef} />
      <Panel header={`Warehouse Assignments for ${entityName}`} toggleable>
        <div className="mb-3">
          <p>Select which warehouses this {entityType} should be visible in:</p>
          <MultiSelect
            value={selectedWarehouses}
            options={warehouses}
            onChange={(e) => setSelectedWarehouses(e.value)}
            optionLabel="Name"
            placeholder="Select Warehouses"
            className="w-full"
            display="chip"
          />
        </div>

        <Button
          label="Save Assignments"
          onClick={handleSaveAssignments}
          loading={loading}
          disabled={loading}
        />

        <div className="mt-3">
          <h3>Currently Assigned Warehouses</h3>
          <DataTable
            value={assignedWarehouses}
            emptyMessage="No warehouses assigned"
            loading={loading}
            size="small"
          >
            <Column
              field="Warehouse.Name"
              header="Warehouse Name"
              body={(rowData) => rowData.Warehouse?.Name}
            />
          </DataTable>
        </div>
      </Panel>
    </>
  );
}
