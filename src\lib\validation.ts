/**
 * Validation utilities for the Peterson Receipt Register application
 */

/**
 * Regular expression for validating email addresses
 */
export const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

/**
 * Validates if a string is a properly formatted email address
 * @param email - The email string to validate
 * @returns boolean - True if the email is valid, false otherwise
 */
export function isValidEmail(email: string): boolean {
  return EMAIL_REGEX.test(email);
}
