'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthStore } from '@/store';
import { Message } from 'primereact/message';
import { Button } from 'primereact/button';

const LoginContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login, authenticated, isUserLoading } = useAuthStore();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [hasError, setHasError] = useState(false);
  const autoLoginAttempted = useRef(false);

  const handleAzureLogin = async () => {
    setErrorMessage(null);
    setHasError(false);
    await login();
  };

  useEffect(() => {
    // If user is already authenticated, redirect to home page
    if (authenticated) {
      router.push('/');
      return;
    }

    // Check for error in URL parameters
    const error = searchParams.get('error');

    if (error) {
      // Handle specific error types
      if (
        error === 'AccessDenied' ||
        error === 'Configuration' ||
        error === 'Verification'
      ) {
        setErrorMessage(
          'Access denied. You do not have permission to access this system. Please contact your system administrator for assistance.'
        );
      } else if (error === 'azure-ad') {
        // Handle Azure AD specific errors
        setErrorMessage(
          'Microsoft authentication service is temporarily unavailable. Please try again in a moment.'
        );
      } else {
        setErrorMessage('An error occurred during sign in. Please try again.');
      }
      setHasError(true);
      autoLoginAttempted.current = false; // Reset the auto-login flag when there's an error
    } else if (!autoLoginAttempted.current && !isUserLoading) {
      // Only trigger auto-login once and when not already loading
      autoLoginAttempted.current = true;
      handleAzureLogin();
    }
  }, [authenticated, router, searchParams, isUserLoading, handleAzureLogin]);

  return (
    <div className="flex align-items-center justify-content-center min-h-screen">
      <div className="surface-card p-6 shadow-2 border-round mx-auto max-w-md">
        <div className="text-center mb-5">
          <div className="text-900 text-3xl font-medium mb-3">Welcome</div>
          <p className="text-600 mb-4">
            {hasError ? 'Sign-in Error' : 'Redirecting to Microsoft Sign-in...'}
          </p>
        </div>

        {errorMessage && (
          <div className="mb-4">
            <Message severity="error" text={errorMessage} className="w-full" />

            {hasError && (
              <div className="mt-4 flex justify-content-center">
                <Button
                  label="Try Again"
                  icon="pi pi-refresh"
                  className="p-button-primary"
                  onClick={handleAzureLogin}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default LoginContent;
