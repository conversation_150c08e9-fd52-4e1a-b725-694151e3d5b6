'use client';
import { useEffect, useState } from 'react';
import axiosInstance from '@/lib/axios';

import { Chart } from 'primereact/chart';
import {
  fetchQuarantinedItemsCount,
  fetchMonthlyStats,
  fetchActiveItemsCount,
} from '../admin.services';
import { ProgressSpinner } from 'primereact/progressspinner';

// Add month abbreviations
const months = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
];

const StatisticsManagement = () => {
  const [activeItemsCount, setActiveItemsCount] = useState<number | null>(null);
  const [quarantinedItemsCount, setQuarantinedItemsCount] = useState<
    number | null
  >(null);
  const [monthlyItemsStats, setMonthlyItemsStats] = useState<
    { Year: number; Month: number; Count: number }[]
  >([]);
  const [warehouseMonthlyStats, setWarehouseMonthlyStats] = useState<any[]>([]);
  const [quarantineStatusCounts, setQuarantineStatusCounts] = useState({
    Pending: 0,
    Closed: 0,
  });
  const [loadListCounts, setLoadListCounts] = useState<any[]>([]);

  useEffect(() => {
    (async () => {
      // Fetch active items count
      const activeItems = await fetchActiveItemsCount();
      setActiveItemsCount(activeItems.length);
      // Fetch quarantined items count
      const quarantined = await fetchQuarantinedItemsCount();
      setQuarantinedItemsCount(quarantined);
      // Fetch monthly stats
      const monthlyStats = await fetchMonthlyStats();
      setMonthlyItemsStats(monthlyStats);
      const { data } = await axiosInstance.get('/items/stats');
      setWarehouseMonthlyStats(data.monthlyStatsByWarehouse);
      setQuarantineStatusCounts(data.quarantinedStats);
      setLoadListCounts(data.loadListStats);
    })();
  }, []);

  // Update chartData labels to show abbreviated month
  const chartData = {
    labels: monthlyItemsStats.map((d) => `${months[d.Month - 1]} ${d.Year}`),
    datasets: [
      {
        label: 'Monthly Items',
        data: monthlyItemsStats.map((d) => d.Count),
      },
    ],
  };

  // Update stackedData labels similarly
  const stackedData = {
    labels: [
      ...Array.from(
        new Set(
          warehouseMonthlyStats.map((d) => `${months[d.Month - 1]} ${d.Year}`)
        )
      ),
    ],
    datasets: [...generatePerWarehouseDatasets(warehouseMonthlyStats)],
  };

  const quarantinedPieData = {
    labels: ['Pending', 'Resolved'],
    datasets: [
      {
        data: [quarantineStatusCounts.Pending, quarantineStatusCounts.Closed],
      },
    ],
  };

  // Update loadListBarData labels to show abbreviated month
  const loadListBarData = {
    labels: loadListCounts.map((d) => `${months[d.Month - 1]} ${d.Year}`),
    datasets: [
      {
        label: 'Load List',
        data: loadListCounts.map((d) => d.Count),
      },
    ],
  };

  // Helper function to build stacked datasets
  function generatePerWarehouseDatasets(stats: any[]) {
    const grouped: Record<string, Record<string, number>> = {};
    stats.forEach((item) => {
      if (!grouped[item.WarehouseName]) grouped[item.WarehouseName] = {};
      const key = `${item.Year}-${item.Month}`;
      if (!grouped[item.WarehouseName][key])
        grouped[item.WarehouseName][key] = 0;
      grouped[item.WarehouseName][key] += item.Count;
    });

    const allKeys = Array.from(
      new Set(stats.map((d) => `${d.Year}-${d.Month}`))
    );
    return Object.keys(grouped).map((warehouse) => ({
      label: warehouse,
      data: allKeys.map((k) => grouped[warehouse][k] || 0),
    }));
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-semibold mb-2">
        Total Items:{' '}
        {activeItemsCount === null ? (
          <ProgressSpinner
            style={{ width: '20px', height: '20px' }}
            strokeWidth="4"
          />
        ) : (
          activeItemsCount
        )}
      </h2>
      <h3 className="text-lg font-medium mb-4">
        Quarantined Items:{' '}
        {quarantinedItemsCount === null ? (
          <ProgressSpinner
            style={{ width: '20px', height: '20px' }}
            strokeWidth="4"
          />
        ) : (
          quarantinedItemsCount
        )}
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 className="text-md font-medium mb-2">Number of Entries</h3>
          <Chart type="bar" data={chartData} options={{}} />
        </div>

        <div>
          <h3 className="text-md font-medium mb-2">
            Number of Entries By Warehouse
          </h3>
          <Chart
            type="bar"
            data={stackedData}
            options={{
              scales: { x: { stacked: true }, y: { stacked: true } },
            }}
          />
        </div>

        <div>
          <h3 className="text-md font-medium mb-2">Quarantined Status</h3>
          <Chart
            type="pie"
            data={quarantinedPieData}
            height="350px"
            options={{
              maintainAspectRatio: false,
              layout: {
                padding: {
                  top: '20',
                },
              },
            }}
          />
        </div>

        <div>
          <h3 className="text-md font-medium mb-2">Load List Count</h3>
          <Chart type="bar" data={loadListBarData} options={{}} />
        </div>
      </div>
    </div>
  );
};

export default StatisticsManagement;
