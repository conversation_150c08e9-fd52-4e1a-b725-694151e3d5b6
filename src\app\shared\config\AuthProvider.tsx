'use client';

import { Session<PERSON>rovider, useSession } from 'next-auth/react';
import { ReactNode, useEffect, useState } from 'react';
import { useAuthStore } from '@/store';
import { usePathname, useRouter } from 'next/navigation';

interface AuthProviderProps {
  children: ReactNode;
}

function AuthInit() {
  const { status, data: session } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const [isRedirecting, setIsRedirecting] = useState(false);

  const setAuthentication = useAuthStore((s) => s.setAuthentication);
  const fetchAndSetUser = useAuthStore((s) => s.fetchAndSetUser);
  const login = useAuthStore((s) => s.login);

  const user = useAuthStore((s) => s.user);

  useEffect(() => {
    if (status === 'authenticated' && session && !user) {
      setAuthentication(true);
      // Pass the session data to avoid redundant API calls
      fetchAndSetUser(session);
    } else if (status === 'unauthenticated' && !isRedirecting) {
      // Skip login redirect if we're already on the login page
      // This prevents double login attempts
      if (pathname === '/login') {
        return;
      }

      // Check for error parameters
      if (typeof window !== 'undefined') {
        const searchParams = new URL(window.location.href).searchParams;
        const error = searchParams.get('error');
        if (error) {
          setIsRedirecting(true);
          router.push('/login');
          return;
        }
      }

      // Set redirecting flag and redirect to login page
      // Let the login page handle the actual Azure AD redirect
      setIsRedirecting(true);
      router.push('/login');
    }
  }, [
    status,
    session,
    user,
    fetchAndSetUser,
    setAuthentication,
    router,
    pathname,
    login,
    isRedirecting,
  ]);

  return null;
}

export default function AuthProvider({ children }: AuthProviderProps) {
  return (
    <SessionProvider>
      <AuthInit />
      {children}
    </SessionProvider>
  );
}
