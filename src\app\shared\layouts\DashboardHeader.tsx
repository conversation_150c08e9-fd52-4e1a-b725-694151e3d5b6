'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuthStore } from '@/store/auth.store';
import { Tag } from 'primereact/tag';
import { useSession } from 'next-auth/react';

export default function DashboardHeader() {
  const pathname = usePathname();
  const user = useAuthStore((state) => state.user);
  const isUserLoading = useAuthStore((state) => state.isUserLoading);
  const { data: session } = useSession();

  const activeClass = (route: string) =>
    route === pathname ? 'underline' : '';

  // Get user name from auth store or session
  const userName = user?.Name || session?.user?.name || 'Guest';
  const userEmail = user?.Email || session?.user?.email;

  return (
    <header className="sticky top-0 z-50 px-4 py-3 border-b border-gray-300 dark:border-gray-700 bg-white shadow-sm">
      <div className="grid grid-cols-3 items-center">
        <nav className="flex gap-4">
          {/* <Link href="/">
            <span className={activeClass('/')}>Home</span>
          </Link> */}
          <Link href="/receipts">
            <span className={activeClass('/receipts')}>Receipts</span>
          </Link>
          <Link href="/quarantine-register">
            <span className={activeClass('/quarantine-register')}>
              Quarantine
            </span>
          </Link>
        </nav>
        <div className="flex justify-center items-center gap-2">
          <i className="pi pi-box text-2xl" style={{ fontSize: '1.5rem' }} />
          <h1 className="text-lg font-semibold flex items-center">
            Pack{' '}
            <Tag
              severity="info"
              value="Lite"
              className="text-sm ml-1  !font-medium !bg-blue-500 !px-2 !rounded-md"
            />
          </h1>
        </div>
        <div className="flex justify-end items-center gap-4">
          <div className="flex items-center gap-2">
            <i className="pi pi-user text-lg" />
            <div className="flex flex-col">
              <span className="font-medium">
                {isUserLoading ? (
                  <i className="pi pi-spinner animate-spin text-sm" />
                ) : (
                  userName
                )}
              </span>
            </div>
          </div>

          {(user?.Role === 'admin' || user?.Role === 'system_admin') && (
            <Link href="/admin">
              <span className={activeClass('/admin')}>Admin</span>
            </Link>
          )}
        </div>
      </div>
    </header>
  );
}
