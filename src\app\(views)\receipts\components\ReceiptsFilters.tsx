import { InputText } from 'primereact/inputtext';
import { But<PERSON> } from 'primereact/button';
import { InputSwitch } from 'primereact/inputswitch';

interface ReceiptsHeaderProps {
  globalFilter: string;
  setGlobalFilter: (value: string) => void;
  filterQuarantined: boolean;
  setFilterQuarantined: (value: boolean) => void;
}

function ReceiptsHeader({
  globalFilter,
  setGlobalFilter,
  filterQuarantined,
  setFilterQuarantined,
}: ReceiptsHeaderProps) {
  return (
    <div className="flex justify-between items-center gap-4 w-full">
      <div className="flex gap-4 items-center">
        <div className="p-inputgroup flex-1">
          <InputText
            type="search"
            placeholder="Search..."
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
          />
          <Button icon="pi pi-search" />
        </div>

        <div className="flex items-center gap-2">
          <InputSwitch
            id="quarantined-filter"
            checked={filterQuarantined}
            onChange={(e) => setFilterQuarantined(e.value as boolean)}
          />
          <label
            htmlFor="quarantined-filter"
            className="cursor-pointer select-none"
            onClick={(e) => setFilterQuarantined(!filterQuarantined)}
          >
            Quarantined Only
          </label>
        </div>
      </div>
    </div>
  );
}

export default ReceiptsHeader;
