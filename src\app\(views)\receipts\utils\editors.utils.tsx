import { InputText } from 'primereact/inputtext';
import { Calendar } from 'primereact/calendar';
import { InputNumber } from 'primereact/inputnumber';
import { Dropdown } from 'primereact/dropdown';
import { MultiSelect } from 'primereact/multiselect';

export const textEditor = (options: any) => (
  <InputText
    value={options.value || ''}
    style={{ width: '100%' }}
    onChange={(e) => options.editorCallback(e.target.value)}
  />
);

export const dateTimeEditor = (options: any) => {
  const dateValue = options.value ? new Date(options.value) : null;
  return (
    <Calendar
      value={dateValue}
      style={{ width: '100%' }}
      onChange={(e) => options.editorCallback(e.value)}
      showTime
      dateFormat="dd/mm/yy"
      showButtonBar
    />
  );
};

export const numberEditor = (options: any) => (
  <InputNumber
    value={options.value}
    style={{ width: '100%' }}
    onValueChange={(e) => options.editorCallback(e.value)}
    min={0}
  />
);

export const dropdownEditor = (
  options: any,
  data: any[],
  optionLabel: string,
  optionValue: string,
  placeholder: string
) => (
  <Dropdown
    value={options.value}
    options={data}
    style={{ width: '100%' }}
    onChange={(e) => options.editorCallback(e.value)}
    optionLabel={optionLabel}
    optionValue={optionValue}
    placeholder={placeholder}
    filter
  />
);

export const multiSelectEditor = (
  options: {
    value: string | string[];
    editorCallback?: (value: string[]) => void;
  },
  data: any[],
  optionLabel: string,
  optionValue: string,
  placeholder: string
) => {
  // Ensure value is always an array
  let valueArray: string[] = [];
  
  try {
    if (Array.isArray(options.value)) {
      valueArray = options.value;
    } else if (typeof options.value === 'string') {
      // Try to parse as JSON if it looks like a JSON array
      if (options.value.startsWith('[') && options.value.endsWith(']')) {
        try {
          const parsed = JSON.parse(options.value);
          valueArray = Array.isArray(parsed) ? parsed : [options.value];
        } catch (e) {
          valueArray = [options.value];
        }
      } else {
        valueArray = [options.value];
      }
    }
  } catch (e) {
    console.error('Error processing package type value:', e);
    valueArray = [];
  }
  
  return (
    <MultiSelect
      value={valueArray}
      options={data}
      style={{ width: '100%' }}
      onChange={(e) => options.editorCallback && options.editorCallback(e.value)}
      optionLabel={optionLabel}
      optionValue={optionValue}
      placeholder={placeholder}
      className="w-full"
      display="comma"
      filter
    />
  );
};
