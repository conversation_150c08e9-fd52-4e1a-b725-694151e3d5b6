'use client';

import useS<PERSON> from 'swr';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { useRef, useState } from 'react';
import ReusableToast from '@/app/shared/components/ReusableToast';
import {
  fetchWarehouses,
  createWarehouse,
  updateWarehouse,
  deleteWarehouse,
  fetchUsers,
  fetchUserWarehouseAssignments,
  assignUserToWarehouse,
  removeUserFromWarehouse,
} from '../admin.services';
import { InputText } from 'primereact/inputtext';
import { textEditor } from '../../receipts/utils/editors.utils';
import { openDeleteConfirmDialog } from '../../receipts/components/utils/ReceiptsDialogsSettings';
import ReusableConfirmDialog from '@/app/shared/components/ReusableConfirmDialog';
import { useWarehouseStore } from '@/store';
import { useAuthStore } from '@/store';
import { Role } from '@/app/shared/models/global.enums';
import ReusableDialog from '@/app/shared/components/ReusableDialog';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash, faLock } from '@fortawesome/free-solid-svg-icons';

function WarehousesManagement() {
  const userEmail = useAuthStore((state) => state.user?.Email);
  const isUserLoading = useAuthStore((state) => state.isUserLoading);
  const userRole = useAuthStore((state) => state.user?.Role);

  // Debug logging
  console.log('User Role in warehouses page:', userRole);
  console.log('Is system admin check:', userRole === Role.SystemAdmin);
  console.log('User object:', useAuthStore.getState().user);

  const {
    data: warehouses,
    error,
    mutate,
  } = useSWR(
    userEmail ? '/admin/warehouses' : null,
    userEmail ? fetchWarehouses : null
  );

  const [newWarehouse, setNewWarehouse] = useState({ Name: '' });
  const toastRef = useRef<any>(null);
  const [editingRowId, setEditingRowId] = useState<string | null>(null);
  const { retriggerFetchWarehouses } = useWarehouseStore();
  const [assignUsersDialogVisible, setAssignUsersDialogVisible] =
    useState(false);
  const [warehouseToAssign, setWarehouseToAssign] = useState<any>(null);

  const onRowEditInit = (event: any) => {
    // Prevent non-SystemAdmin users from editing warehouses
    if (userRole !== Role.SystemAdmin) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Permission Denied',
        detail: 'Only System Admins can edit warehouses',
      });
      return;
    }
    setEditingRowId(event.data ? event.data.WarehouseId : event.WarehouseId);
  };

  const onRowEditCancel = () => setEditingRowId(null);

  const onRowEditComplete = async (e: any) => {
    try {
      await updateWarehouse(e.newData.WarehouseId, { Name: e.newData.Name });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Updated',
        detail: 'Warehouse updated',
      });
      mutate();
      retriggerFetchWarehouses();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update warehouse',
      });
    } finally {
      setEditingRowId(null);
    }
  };

  const handleDelete = async (rowData: any) => {
    try {
      await deleteWarehouse(rowData.WarehouseId);
      toastRef.current?.show({
        severity: 'success',
        summary: 'Deleted',
        detail: 'Warehouse deleted',
      });
      mutate();
      retriggerFetchWarehouses();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to delete warehouse',
      });
    }
  };

  const confirmDelete = (rowData: any) => {
    openDeleteConfirmDialog({ onAccept: () => handleDelete(rowData) });
  };

  const handleCreate = async () => {
    try {
      await createWarehouse(newWarehouse);
      setNewWarehouse({ Name: '' });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Created',
        detail: 'Warehouse created',
      });
      mutate();
      retriggerFetchWarehouses();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to create warehouse',
      });
    }
  };

  const shouldFetchUsers = assignUsersDialogVisible && warehouseToAssign;
  const { data: users, mutate: mutateUsers } = useSWR(
    shouldFetchUsers ? '/admin/users' : null,
    shouldFetchUsers ? fetchUsers : null
  );
  const { data: assignments, mutate: mutateAssignments } = useSWR(
    shouldFetchUsers
      ? `user-warehouse-assignments-${warehouseToAssign?.WarehouseId}`
      : null,
    shouldFetchUsers ? () => fetchUserWarehouseAssignments('all') : null
  );

  // Get the current logged in user
  const loggedInUser = useAuthStore((state) => state.user);

  // Filter out the current user from the users list
  const filteredUsers = users?.filter((user: any) => user.Email !== userEmail);

  // Check if user has permission to create warehouses
  const canCreateWarehouses = () => {
    return userRole === Role.SystemAdmin;
  };

  // Check if user has permission to manage a specific warehouse
  const canManageWarehouse = () => {
    return userRole === Role.SystemAdmin || userRole === Role.Admin;
  };

  const openAssignUsersDialog = (rowData: any) => {
    setWarehouseToAssign(rowData);
    setAssignUsersDialogVisible(true);
    mutateUsers();
    mutateAssignments();
  };

  const isUserAssigned = (userId: string) => {
    return assignments?.some(
      (a: any) =>
        a.UserId === userId && a.WarehouseId === warehouseToAssign?.WarehouseId
    );
  };

  const handleAssign = async (userId: string) => {
    if (!warehouseToAssign) return;

    // Admin users can assign users to any warehouse they can see
    // No need for additional permission check here

    // Get the user being assigned
    const userToAssign = users?.find((u: any) => u.UserId === userId);

    // Check if admin is trying to assign another admin to a warehouse
    if (userRole === Role.Admin && userToAssign?.Role === Role.Admin) {
      // Allow admins to assign other admins only to warehouses they belong to
      if (!canManageWarehouse()) {
        toastRef.current?.show({
          severity: 'error',
          summary: 'Permission Denied',
          detail: 'You can only assign admin users to warehouses you belong to',
        });
        return;
      }
    }

    await assignUserToWarehouse({
      UserId: userId,
      WarehouseId: warehouseToAssign.WarehouseId,
    });
    mutateAssignments();
  };

  const handleRemove = async (userId: string) => {
    if (!warehouseToAssign) return;

    // Admin users can remove users from any warehouse they can see
    // No need for additional permission check here

    // Get the user being removed
    const userToRemove = users?.find((u: any) => u.UserId === userId);

    // Check if admin is trying to remove a SystemAdmin
    if (userRole === Role.Admin && userToRemove?.Role === Role.SystemAdmin) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Permission Denied',
        detail: 'You cannot remove System Admin users',
      });
      return;
    }

    await removeUserFromWarehouse({
      UserId: userId,
      WarehouseId: warehouseToAssign.WarehouseId,
    });
    mutateAssignments();
  };

  if (isUserLoading) return <p>Loading user data...</p>;
  if (!warehouses) return <p>Loading warehouses...</p>;
  if (error) return <p>Error loading warehouses</p>;

  return (
    <>
      <h1>Warehouses Management</h1>
      <div className="flex gap-2 my-2">
        <InputText
          type="text"
          placeholder="Name"
          value={newWarehouse.Name}
          onChange={(e) => setNewWarehouse({ Name: e.target.value })}
          disabled={!canCreateWarehouses()}
        />
        <Button
          disabled={!newWarehouse.Name || !canCreateWarehouses()}
          label="Create Warehouse"
          onClick={handleCreate}
          tooltip={
            !canCreateWarehouses()
              ? 'Only System Admins can create warehouses'
              : ''
          }
          tooltipOptions={{ position: 'top' }}
        />
      </div>
      <DataTable
        value={warehouses}
        editMode="row"
        dataKey="WarehouseId"
        size="small"
        stripedRows
        onRowEditComplete={onRowEditComplete}
        onRowEditCancel={onRowEditCancel}
        onRowEditInit={onRowEditInit}
      >
        <Column
          field="Name"
          header="Name"
          editor={(options) => textEditor(options)}
        />
        {userRole === Role.SystemAdmin ? (
          // Show edit and delete buttons for system admins
          <>
            <Column rowEditor headerStyle={{ width: '4rem' }} />
            <Column
              body={(rowData) => (
                <Button
                  icon={<FontAwesomeIcon icon={faTrash} />}
                  className="p-button-text p-button-rounded p-button-danger"
                  onClick={() => confirmDelete(rowData)}
                  tooltip="Delete"
                  tooltipOptions={{ position: 'top' }}
                />
              )}
              headerStyle={{ width: '4rem' }}
            />
          </>
        ) : (
          // Show lock icon for non-system admins
          <Column
            headerStyle={{ width: '8rem' }}
            body={(rowData) => (
              <Button
                icon={<FontAwesomeIcon icon={faLock} />}
                className="p-button-text p-button-rounded p-button-secondary"
                disabled={true}
                tooltip="Only System Administrators can edit or delete warehouses"
                tooltipOptions={{ position: 'top' }}
              />
            )}
          />
        )}
        <Column
          body={(rowData) => (
            <Button
              label="Users"
              onClick={() => openAssignUsersDialog(rowData)}
              className="p-button-sm"
              tooltip="Assign Users"
              tooltipOptions={{ position: 'top' }}
              disabled={!canManageWarehouse()}
            />
          )}
          headerStyle={{ width: '6rem' }}
          bodyStyle={{ textAlign: 'center' }}
        />
      </DataTable>

      <ReusableDialog
        visible={assignUsersDialogVisible}
        onHide={() => setAssignUsersDialogVisible(false)}
        header={`Assign Users to ${warehouseToAssign?.Name || ''}`}
      >
        {filteredUsers && assignments ? (
          <div>
            {filteredUsers.map((user: any) => {
              // Check if admin can assign this user based on roles
              const canAssignUser =
                userRole === Role.SystemAdmin ||
                (userRole === Role.Admin && user.Role !== Role.SystemAdmin);

              // For admins, allow assignment to any warehouse they can see
              const canAssignToWarehouse =
                userRole === Role.SystemAdmin || userRole === Role.Admin;

              return (
                <div
                  key={user.UserId}
                  className="flex items-center justify-between mb-2"
                >
                  <span>
                    {user.Name} ({user.Email}) - {user.Role}
                  </span>
                  {isUserAssigned(user.UserId) ? (
                    <Button
                      label="Remove"
                      onClick={() => handleRemove(user.UserId)}
                      className="p-button-secondary"
                      disabled={!canAssignUser || !canAssignToWarehouse}
                      tooltip={
                        !canAssignUser
                          ? 'You cannot manage this user'
                          : !canAssignToWarehouse
                            ? 'You cannot manage this warehouse'
                            : ''
                      }
                      tooltipOptions={{ position: 'top' }}
                    />
                  ) : (
                    <Button
                      label="Assign"
                      onClick={() => handleAssign(user.UserId)}
                      disabled={!canAssignUser || !canAssignToWarehouse}
                      tooltip={
                        !canAssignUser
                          ? 'You cannot manage this user'
                          : !canAssignToWarehouse
                            ? 'You cannot manage this warehouse'
                            : ''
                      }
                      tooltipOptions={{ position: 'top' }}
                    />
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <p>Loading users...</p>
        )}
      </ReusableDialog>

      <ReusableToast ref={toastRef} />
      <ReusableConfirmDialog />
    </>
  );
}

export default WarehousesManagement;
