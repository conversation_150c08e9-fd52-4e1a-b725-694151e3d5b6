import React from 'react';
import ReusableTag from './ReusableTag';
import { parsePackageTypes } from '../utils/packageTypes.utils';

interface PackageTypeTagsProps {
  packageType: string | string[] | unknown;
  className?: string;
}

/**
 * A component to display package types as tags
 * @param packageType - The package type value(s) to display
 * @param className - Additional CSS classes to apply to the container
 */
function PackageTypeTags({
  packageType,
  className = '',
}: PackageTypeTagsProps) {
  const packageTypes = parsePackageTypes(packageType);
  
  return (
    <div className={`flex flex-wrap gap-1 ${className}`}>
      {packageTypes.map((type: string, index: number) => (
        <ReusableTag 
          key={index} 
          value={type} 
          color="custom" 
          customColor="#4868D1" 
        />
      ))}
    </div>
  );
}

export default PackageTypeTags;
