/**
 * Utility functions for handling package types
 */

/**
 * Parses a package type value (string, array, or JSON string) into a string array
 * @param value - The package type value to parse
 * @returns An array of package types
 */
export const parsePackageTypes = (value: unknown): string[] => {
  try {
    if (Array.isArray(value)) {
      return value;
    } 
    
    if (typeof value === 'string') {
      // Try to parse as JSON if it looks like a JSON array
      if (value.startsWith('[') && value.endsWith(']')) {
        try {
          const parsed = JSON.parse(value);
          return Array.isArray(parsed) ? parsed : [value];
        } catch {
          // If <PERSON>SO<PERSON> parsing fails, treat as a single string
          return [value];
        }
      }
      // Not JSON, treat as a single string
      return [value];
    }
    
    // Handle null/undefined
    if (value === null || value === undefined) {
      return [];
    }
    
    // For any other type, convert to string and use as single value
    return [String(value)];
  } catch (e) {
    console.error('Error parsing package types:', e);
    return [];
  }
};

/**
 * Converts a package type value (string or array) to a JSON string for storage
 * @param value - The package type value to convert
 * @returns A JSON string representation of the package types
 */
export const stringifyPackageTypes = (value: unknown): string => {
  try {
    if (Array.isArray(value)) {
      return JSON.stringify(value);
    }
    
    if (typeof value === 'string') {
      // Check if it already looks like a JSON array
      if (value.startsWith('[') && value.endsWith(']')) {
        // Validate it's proper JSON
        try {
          JSON.parse(value);
          return value; // It's already valid JSON
        } catch {
          // Not valid JSON, treat as a single string
          return JSON.stringify([value]);
        }
      }
      // Not JSON, convert single string to JSON array
      return JSON.stringify([value]);
    }
    
    // Handle null/undefined
    if (value === null || value === undefined) {
      return JSON.stringify([]);
    }
    
    // For any other type, convert to string and use as single value
    return JSON.stringify([String(value)]);
  } catch (e) {
    console.error('Error stringifying package types:', e);
    return JSON.stringify([]);
  }
};
