'use client';

import useS<PERSON> from 'swr';
import { useRef, useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import ReusableToast from '@/app/shared/components/ReusableToast';
import { But<PERSON> } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { textEditor } from '../../receipts/utils/editors.utils';
import { openDeleteConfirmDialog } from '../../receipts/components/utils/ReceiptsDialogsSettings';
import ReusableConfirmDialog from '@/app/shared/components/ReusableConfirmDialog';
import { fetchVendors, createVendor, updateVendor } from '../admin.services';
import { useAuthStore } from '@/store';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash } from '@fortawesome/free-solid-svg-icons';

export default function VendorsManagement() {
  const {
    data: vendors,
    error,
    mutate,
  } = useSWR('/admin/vendors', fetchVendors);
  const [newVendor, setNewVendor] = useState({ Name: '' });
  const [editingRowId, setEditingRowId] = useState<string | null>(null);
  const toastRef = useRef<any>(null);

  const isUserLoading = useAuthStore((state) => state.isUserLoading);
  if (isUserLoading) return <p>Loading user data...</p>;

  const onRowEditInit = (e: any) => setEditingRowId(e.data.VendorId);
  const onRowEditCancel = () => setEditingRowId(null);

  const onRowEditComplete = async (e: any) => {
    try {
      await updateVendor(e.newData.VendorId, { Name: e.newData.Name });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Updated',
        detail: 'Vendor updated',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update vendor',
      });
    } finally {
      setEditingRowId(null);
    }
  };

  const handleCreate = async () => {
    try {
      await createVendor(newVendor);
      setNewVendor({ Name: '' });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Created',
        detail: 'Vendor created',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to create vendor',
      });
    }
  };

  async function handleDelete(v: any) {
    try {
      await updateVendor(v.VendorId, { IsDeleted: true }); // soft-delete
      toastRef.current?.show({
        severity: 'success',
        summary: 'Deleted',
        detail: 'Vendor deleted',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to delete vendor',
      });
    }
  }

  const confirmDelete = (rowData: any) => {
    openDeleteConfirmDialog({ onAccept: () => handleDelete(rowData) });
  };

  if (!vendors) return <p>Loading...</p>;
  if (error) return <p>Error loading vendors</p>;

  return (
    <>
      <ReusableToast ref={toastRef} />
      <h1>Vendors Management</h1>
      <div className="flex gap-2 my-2">
        <InputText
          placeholder="Name"
          value={newVendor.Name}
          onChange={(e) => setNewVendor({ Name: e.target.value })}
        />
        <Button
          disabled={!newVendor.Name}
          label="Create Vendor"
          onClick={handleCreate}
        />
      </div>

      <DataTable
        value={vendors}
        editMode="row"
        dataKey="VendorId"
        size="small"
        stripedRows
        onRowEditInit={onRowEditInit}
        onRowEditCancel={onRowEditCancel}
        onRowEditComplete={onRowEditComplete}
      >
        {/* <Column field="VendorId" header="ID" /> */}
        <Column
          field="Name"
          header="Name"
          editor={(opts) => textEditor(opts)}
        />
        <Column rowEditor headerStyle={{ width: '4rem' }} />
        <Column
          body={(rowData) => (
            <Button
              icon={<FontAwesomeIcon icon={faTrash} />}
              className="p-button-text p-button-rounded p-button-danger"
              onClick={() => confirmDelete(rowData)}
              tooltip="Delete"
              tooltipOptions={{ position: 'top' }}
            />
          )}
          headerStyle={{ width: '6rem' }}
        />
      </DataTable>

      <ReusableConfirmDialog />
    </>
  );
}
