# Prisma Migrations

## Running Migrations

To apply pending migrations to staging, testing, or production environments, run the migrate deploy command as part of your CI/CD pipeline:

```sh
npx prisma migrate deploy
```

To generate and apply a new migration based on your Prisma schema changes. It creates migration files that keep a history of changes.

> [!CAUTION]
> migrate dev is a development command and should never be used in a production environment.
```sh
npx prisma migrate dev
```

This command will apply any pending migrations to your database.

## Creating a New Migration

To create a new migration, use the following command:

```sh
npx prisma migrate dev --name <migration_name>
```

## Checking Migration Status

To check the status of your migrations, use the following command:

```sh
npx prisma migrate status
```

This command will show you the current status of your migrations.
