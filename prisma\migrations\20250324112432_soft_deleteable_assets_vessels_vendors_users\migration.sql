BEGIN TRY

BEGIN TRAN;

-- AlterTable
ALTER TABLE [dbo].[Asset] DROP CONSTRAINT [Asset_AssetId_df];
ALTER TABLE [dbo].[Asset] ADD CONSTRAINT [Asset_AssetId_df] DEFAULT (newid()) FOR [AssetId];
ALTER TABLE [dbo].[Asset] ADD [IsDeleted] BIT NOT NULL CONSTRAINT [Asset_IsDeleted_df] DEFAULT 0;

-- AlterTable
ALTER TABLE [dbo].[Item] DROP CONSTRAINT [Item_ItemId_df];
ALTER TABLE [dbo].[Item] ADD CONSTRAINT [Item_ItemId_df] DEFAULT (newid()) FOR [ItemId];

-- AlterTable
ALTER TABLE [dbo].[User] DROP CONSTRAINT [User_UserId_df];
ALTER TABLE [dbo].[User] ADD CONSTRAINT [User_UserId_df] DEFAULT (newid()) FOR [UserId];
ALTER TABLE [dbo].[User] ADD [IsDeleted] BIT NOT NULL CONSTRAINT [User_IsDeleted_df] DEFAULT 0;

-- AlterTable
ALTER TABLE [dbo].[UserWarehouse] DROP CONSTRAINT [UserWarehouse_UserWarehouseId_df];
ALTER TABLE [dbo].[UserWarehouse] ADD CONSTRAINT [UserWarehouse_UserWarehouseId_df] DEFAULT (newid()) FOR [UserWarehouseId];

-- AlterTable
ALTER TABLE [dbo].[Vendor] DROP CONSTRAINT [Vendor_VendorId_df];
ALTER TABLE [dbo].[Vendor] ADD CONSTRAINT [Vendor_VendorId_df] DEFAULT (newid()) FOR [VendorId];
ALTER TABLE [dbo].[Vendor] ADD [IsDeleted] BIT NOT NULL CONSTRAINT [Vendor_IsDeleted_df] DEFAULT 0;

-- AlterTable
ALTER TABLE [dbo].[Vessel] DROP CONSTRAINT [Vessel_VesselId_df];
ALTER TABLE [dbo].[Vessel] ADD CONSTRAINT [Vessel_VesselId_df] DEFAULT (newid()) FOR [VesselId];
ALTER TABLE [dbo].[Vessel] ADD [IsDeleted] BIT NOT NULL CONSTRAINT [Vessel_IsDeleted_df] DEFAULT 0;

-- AlterTable
ALTER TABLE [dbo].[Warehouse] DROP CONSTRAINT [Warehouse_WarehouseId_df];
ALTER TABLE [dbo].[Warehouse] ADD CONSTRAINT [Warehouse_WarehouseId_df] DEFAULT (newid()) FOR [WarehouseId];

COMMIT TRAN;

END TRY
BEGIN CATCH

IF @@TRANCOUNT > 0
BEGIN
    ROLLBACK TRAN;
END;
THROW

END CATCH
