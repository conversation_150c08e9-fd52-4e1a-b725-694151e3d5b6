import { IItemCreate } from '@/app/api/items/items.enums';
import {
  IAsset,
  IItem,
  IVendor,
  IVessel,
} from '@/app/shared/models/global.interfaces';
import { PackageType } from '@/app/shared/models/global.enums';
import { useWarehouseStore } from '@/store';
import { useAuthStore } from '@/store';
import axiosInstance from '@/lib/axios';
import checkQuarantineStatus from '@/app/shared/utils/checkQuarantineStatus';

export const fetchItems = async (): Promise<IItem[]> => {
  const { selectedWarehouse } = useWarehouseStore.getState();
  const { user } = useAuthStore.getState();
  let query = '';
  if (selectedWarehouse && selectedWarehouse.WarehouseId !== 'all') {
    query = `?warehouseId=${selectedWarehouse.WarehouseId}`;
  }
  query = query ? `${query}&userId=${user?.UserId}` : `?userId=${user?.UserId}`;
  const response = await axiosInstance.get(`/items${query}`);
  return response.data;
};

// Includes deleted items (soft delete) because the receipt numbers are unique
export const fetchTotalItemsCountIncludingDeleted =
  async (): Promise<number> => {
    const response = await axiosInstance.get(`/items/total`);
    return response.data;
  };

export const createItem = async (item: IItemCreate): Promise<IItemCreate> => {
  const { selectedWarehouse } = useWarehouseStore.getState();
  if (
    selectedWarehouse &&
    selectedWarehouse.WarehouseId &&
    selectedWarehouse.WarehouseId !== 'all'
  ) {
    item.WarehouseId = selectedWarehouse.WarehouseId;
  } else {
    delete item.WarehouseId;
  }
  const response = await axiosInstance.post(`/items`, item);
  return response.data;
};

export const updateItem = async (
  id: string,
  data: Partial<IItem>
): Promise<IItem> => {
  data = checkQuarantineStatus(data);

  const response = await axiosInstance.put(`/items/${id}`, data);
  return response.data;
};

export const fetchAssets = async (): Promise<IAsset[]> => {
  const response = await axiosInstance.get(`/assets`);
  return response.data;
};

export const fetchVendors = async (): Promise<IVendor[]> => {
  const response = await axiosInstance.get(`/vendors`);
  return response.data;
};

export const fetchVessels = async (): Promise<IVessel[]> => {
  const response = await axiosInstance.get(`/vessels`);
  return response.data;
};

export const deleteItems = async (items: IItem[]): Promise<void> => {
  const deletePromises = items.map((item) =>
    axiosInstance.put(`/items`, { ItemId: item.ItemId, isDeleted: true })
  );
  await Promise.all(deletePromises);
};

export const restoreItems = async (items: IItem[]): Promise<void> => {
  const restorePromises = items.map((item) =>
    axiosInstance.put(`/items`, { ItemId: item.ItemId, isDeleted: false })
  );
  await Promise.all(restorePromises);
};

export const updateQuarantineDetails = async (
  id: string,
  quarantineData: {
    ReceiptNumber?: string;
    ReceiptDateTime?: Date | string | null;
    AssetId?: string;
    VendorId?: string;
    DescriptionOfNonConformance?: string;
    NCRNumber?: string;
    Status?: string;
    DateClosed?: string | null;
    IsQuarantined?: boolean;
    PONumber?: string;
    SupplierDeliveryNoteNumber?: string;
    packageType?: string | string[];
    NumberOfPackages?: number | string;
  }
): Promise<IItem> => {
  const updatedQuarantineData = {
    ...quarantineData,
    DateClosed: quarantineData.DateClosed
      ? new Date(quarantineData.DateClosed)
      : null,
    ReceiptDateTime: quarantineData.ReceiptDateTime
      ? new Date(quarantineData.ReceiptDateTime)
      : undefined,
    packageType: quarantineData.packageType
      ? Array.isArray(quarantineData.packageType)
        ? quarantineData.packageType.map((type) =>
            typeof type === 'string'
              ? PackageType[type as keyof typeof PackageType] || type
              : type
          )
        : [
            PackageType[
              quarantineData.packageType as keyof typeof PackageType
            ] || quarantineData.packageType,
          ]
      : [],
    NumberOfPackages: quarantineData.NumberOfPackages
      ? Number(quarantineData.NumberOfPackages)
      : undefined,
  };
  return updateItem(id, updatedQuarantineData);
};

export const fetchNextReceiptNumber = async (): Promise<string> => {
  const response = await axiosInstance.get('/items?action=nextReceiptNumber');
  return response.data.receiptNumber;
};
