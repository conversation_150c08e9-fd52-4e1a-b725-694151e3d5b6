export enum QuarantinedItemStatus {
  Quarantined = 'Quarantined',
  Released = 'Released',
  Disposed = 'Disposed',
}

export enum PackageType {
  Bag = 'Bag',
  Box = 'Box',
  Drum = 'Drum',
  Crate = 'Crate',
  <PERSON><PERSON> = 'Jiffy',
  Other = 'Other',
  Pallet = 'Pallet',
}

export enum Role {
  SystemAdmin = 'system_admin',
  Admin = 'admin',
  Write = 'write',
  Read = 'read',
}

export enum QuarantineStatus {
  Pending = 'Pending',
  Resolved = 'Resolved',
}
